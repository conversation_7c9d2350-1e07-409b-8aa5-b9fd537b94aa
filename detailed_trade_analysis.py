#!/usr/bin/env python3
"""
Detailed Trade Analysis - Focus on Loss Patterns
Analyzes the specific mechanics of losing trades to identify root causes
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_trade_mechanics():
    """Analyze the specific mechanics causing losses"""
    
    print("🔬 DETAILED TRADE MECHANICS ANALYSIS")
    print("=" * 60)
    
    # Load trades data
    trades_df = pd.read_csv('trades/call_spread_trades.csv')
    trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
    trades_df = trades_df.sort_values('exit_date')
    
    print(f"✅ Analyzing {len(trades_df)} trades")
    
    # Separate winning and losing trades
    winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
    losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
    
    print(f"📊 {len(winning_trades)} winning trades, {len(losing_trades)} losing trades")
    
    # Analyze the worst losing trades
    print("\n💔 TOP 10 WORST LOSING TRADES")
    print("-" * 50)
    
    worst_trades = losing_trades.nsmallest(10, 'net_pnl')
    
    for i, (_, trade) in enumerate(worst_trades.iterrows(), 1):
        print(f"\n{i}. Trade on {trade['exit_date'].strftime('%Y-%m-%d')}:")
        print(f"   💰 Loss: ${trade['net_pnl']:,.0f}")
        print(f"   📈 SPX Price: {trade['spx_price']:.0f}")
        print(f"   🎯 Confidence: {trade['confidence_score']:.2f}")
        print(f"   📊 Strikes: {trade['short_strike']:.0f}/{trade['long_strike']:.0f}")
        print(f"   💵 Net Credit: ${trade['net_credit']:.2f}")
        print(f"   📦 Contracts: {trade['contracts']}")
        
        # Calculate what went wrong
        short_move = trade['short_exit_price'] - trade['short_entry_price']
        long_move = trade['long_exit_price'] - trade['long_entry_price']
        
        print(f"   📉 Short leg moved: ${short_move:.2f} (${short_move * trade['contracts'] * 100:,.0f})")
        print(f"   📈 Long leg moved: ${long_move:.2f} (${long_move * trade['contracts'] * 100:,.0f})")
        
        # Determine if SPX moved against us
        if trade['spx_price'] > trade['short_strike']:
            print(f"   ⚠️  SPX ({trade['spx_price']:.0f}) ABOVE short strike ({trade['short_strike']:.0f}) - IN THE MONEY!")
        
        if trade['spx_price'] > trade['long_strike']:
            print(f"   🚨 SPX ({trade['spx_price']:.0f}) ABOVE long strike ({trade['long_strike']:.0f}) - MAX LOSS!")
    
    # Analyze strike selection patterns
    print("\n📊 STRIKE SELECTION ANALYSIS")
    print("-" * 40)
    
    # Calculate distance from SPX to strikes
    trades_df['short_distance'] = trades_df['short_strike'] - trades_df['spx_price']
    trades_df['long_distance'] = trades_df['long_strike'] - trades_df['spx_price']
    
    print(f"📈 Average short strike distance: {trades_df['short_distance'].mean():.0f} points")
    print(f"📈 Average long strike distance: {trades_df['long_distance'].mean():.0f} points")
    
    # Compare winning vs losing strike distances
    winning_trades = trades_df[trades_df['win_loss_flag'] == 1].copy()
    losing_trades = trades_df[trades_df['win_loss_flag'] == 0].copy()

    # Calculate distances for subsets
    winning_trades['short_distance'] = winning_trades['short_strike'] - winning_trades['spx_price']
    winning_trades['long_distance'] = winning_trades['long_strike'] - winning_trades['spx_price']
    losing_trades['short_distance'] = losing_trades['short_strike'] - losing_trades['spx_price']
    losing_trades['long_distance'] = losing_trades['long_strike'] - losing_trades['spx_price']

    print(f"\n🏆 WINNING TRADES:")
    print(f"   📈 Avg short distance: {winning_trades['short_distance'].mean():.0f} points")
    print(f"   📈 Avg long distance: {winning_trades['long_distance'].mean():.0f} points")
    print(f"   💰 Avg net credit: ${winning_trades['net_credit'].mean():.2f}")

    print(f"\n💔 LOSING TRADES:")
    print(f"   📈 Avg short distance: {losing_trades['short_distance'].mean():.0f} points")
    print(f"   📈 Avg long distance: {losing_trades['long_distance'].mean():.0f} points")
    print(f"   💰 Avg net credit: ${losing_trades['net_credit'].mean():.2f}")
    
    # Analyze how often SPX moves beyond strikes
    print("\n🎯 SPX MOVEMENT ANALYSIS")
    print("-" * 30)
    
    # For each trade, check if SPX moved beyond strikes at exit
    trades_df['spx_above_short'] = trades_df['spx_price'] > trades_df['short_strike']
    trades_df['spx_above_long'] = trades_df['spx_price'] > trades_df['long_strike']
    
    above_short_count = trades_df['spx_above_short'].sum()
    above_long_count = trades_df['spx_above_long'].sum()
    
    print(f"📊 SPX above short strike: {above_short_count}/{len(trades_df)} trades ({above_short_count/len(trades_df)*100:.1f}%)")
    print(f"📊 SPX above long strike: {above_long_count}/{len(trades_df)} trades ({above_long_count/len(trades_df)*100:.1f}%)")
    
    # Analyze position sizing impact
    print("\n📦 POSITION SIZING ANALYSIS")
    print("-" * 35)
    
    # Group by contract size
    trades_df['contract_bucket'] = pd.cut(trades_df['contracts'], 
                                        bins=[0, 30, 45, 60, 100],
                                        labels=['Small (≤30)', 'Medium (31-45)', 'Large (46-60)', 'XLarge (>60)'])
    
    sizing_analysis = trades_df.groupby('contract_bucket').agg({
        'net_pnl': ['sum', 'count', 'mean'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    print("📊 Performance by position size:")
    for size in sizing_analysis.index:
        if pd.isna(size):
            continue
        total_pnl = sizing_analysis.loc[size, ('net_pnl', 'sum')]
        trade_count = sizing_analysis.loc[size, ('net_pnl', 'count')]
        avg_pnl = sizing_analysis.loc[size, ('net_pnl', 'mean')]
        win_rate = sizing_analysis.loc[size, ('win_loss_flag', 'mean')] * 100
        
        print(f"   {size}: ${total_pnl:,.0f} ({trade_count} trades, ${avg_pnl:,.0f} avg, {win_rate:.1f}% win rate)")
    
    # Analyze credit received vs actual performance
    print("\n💰 CREDIT vs PERFORMANCE ANALYSIS")
    print("-" * 40)
    
    trades_df['credit_bucket'] = pd.cut(trades_df['net_credit'], 
                                      bins=[0, 40, 60, 80, 200],
                                      labels=['Low (≤$40)', 'Medium ($41-60)', 'High ($61-80)', 'Very High (>$80)'])
    
    credit_analysis = trades_df.groupby('credit_bucket').agg({
        'net_pnl': ['sum', 'count', 'mean'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    print("📊 Performance by credit received:")
    for credit in credit_analysis.index:
        if pd.isna(credit):
            continue
        total_pnl = credit_analysis.loc[credit, ('net_pnl', 'sum')]
        trade_count = credit_analysis.loc[credit, ('net_pnl', 'count')]
        avg_pnl = credit_analysis.loc[credit, ('net_pnl', 'mean')]
        win_rate = credit_analysis.loc[credit, ('win_loss_flag', 'mean')] * 100
        
        print(f"   {credit}: ${total_pnl:,.0f} ({trade_count} trades, ${avg_pnl:,.0f} avg, {win_rate:.1f}% win rate)")
    
    # Key insights and recommendations
    print("\n🎯 KEY INSIGHTS & RECOMMENDATIONS")
    print("=" * 50)
    
    # Calculate key metrics
    avg_loss = losing_trades['net_pnl'].mean()
    avg_win = winning_trades['net_pnl'].mean()
    win_rate = len(winning_trades) / len(trades_df) * 100
    
    print(f"📊 Current Performance:")
    print(f"   🎯 Win Rate: {win_rate:.1f}%")
    print(f"   💪 Average Win: ${avg_win:,.0f}")
    print(f"   💔 Average Loss: ${avg_loss:,.0f}")
    print(f"   ⚖️  Win/Loss Ratio: {abs(avg_win/avg_loss):.2f}")
    
    print(f"\n🔍 Root Cause Analysis:")
    
    # Check if strikes are too close
    avg_short_distance = trades_df['short_distance'].mean()
    if avg_short_distance < 50:
        print(f"   ⚠️  SHORT STRIKES TOO CLOSE: Avg {avg_short_distance:.0f} points from SPX")
        print(f"      💡 Recommendation: Move short strikes further OTM (75-100 points)")
    
    # Check position sizing on losses
    large_loss_trades = trades_df[trades_df['net_pnl'] < -100000]
    if len(large_loss_trades) > 0:
        avg_contracts_large_loss = large_loss_trades['contracts'].mean()
        print(f"   ⚠️  LARGE POSITION SIZES ON LOSSES: Avg {avg_contracts_large_loss:.0f} contracts")
        print(f"      💡 Recommendation: Reduce position sizes, especially on high confidence trades")
    
    # Check confidence score paradox
    high_conf_losses = trades_df[(trades_df['confidence_score'] > 0.9) & (trades_df['win_loss_flag'] == 0)]
    if len(high_conf_losses) > 0:
        print(f"   ⚠️  HIGH CONFIDENCE PARADOX: {len(high_conf_losses)} losses with >90% confidence")
        print(f"      💡 Recommendation: Review signal generation - high confidence may be overfitting")
    
    # Check market regime
    recent_trades = trades_df[trades_df['exit_date'] >= '2024-01-01']
    recent_win_rate = recent_trades['win_loss_flag'].mean() * 100
    if recent_win_rate < 40:
        print(f"   ⚠️  RECENT PERFORMANCE DECLINE: {recent_win_rate:.1f}% win rate in 2024+")
        print(f"      💡 Recommendation: Strategy may not be adapting to current market regime")
    
    return trades_df

if __name__ == "__main__":
    analyze_trade_mechanics()
