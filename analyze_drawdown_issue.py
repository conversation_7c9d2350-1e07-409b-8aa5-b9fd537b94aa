#!/usr/bin/env python3
"""
Analyze the current drawdown issue and identify root causes
"""

import pandas as pd
import numpy as np

def analyze_current_drawdown():
    """Analyze the current performance issues"""
    
    print("🔍 ANALYZING CURRENT DRAWDOWN ISSUE")
    print("=" * 50)
    
    # Load current trades
    trades_df = pd.read_csv('trades/call_spread_trades.csv')
    print(f"📊 Analyzing {len(trades_df)} trades")
    
    # Basic performance metrics
    total_pnl = trades_df['net_pnl'].sum()
    win_rate = trades_df['win_loss_flag'].mean() * 100
    winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
    losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
    
    print(f"\n📈 CURRENT PERFORMANCE:")
    print(f"   Total P&L: ${total_pnl:,.0f}")
    print(f"   Win Rate: {win_rate:.1f}%")
    print(f"   Winning Trades: {len(winning_trades)}")
    print(f"   Losing Trades: {len(losing_trades)}")
    
    if len(winning_trades) > 0:
        print(f"   Avg Win: ${winning_trades['net_pnl'].mean():,.0f}")
    if len(losing_trades) > 0:
        print(f"   Avg Loss: ${losing_trades['net_pnl'].mean():,.0f}")
    
    # Analyze strike selection issues
    print(f"\n🎯 STRIKE SELECTION ANALYSIS:")
    
    # Calculate distance from SPX to strikes
    trades_df['short_distance'] = trades_df['short_strike'] - trades_df['spx_price']
    trades_df['long_distance'] = trades_df['long_strike'] - trades_df['spx_price']
    
    print(f"   Avg short strike distance: {trades_df['short_distance'].mean():.0f} points")
    print(f"   Avg long strike distance: {trades_df['long_distance'].mean():.0f} points")
    
    # Check how often SPX moves against us
    trades_df['spx_vs_short'] = trades_df['spx_price'] - trades_df['short_strike']
    
    # For PUT spreads, we want SPX to stay ABOVE the short strike
    # If SPX < short_strike, the PUT we sold goes ITM (bad for us)
    trades_df['short_itm'] = trades_df['spx_price'] < trades_df['short_strike']
    itm_count = trades_df['short_itm'].sum()
    
    print(f"   SPX below short strike (ITM): {itm_count}/{len(trades_df)} trades ({itm_count/len(trades_df)*100:.1f}%)")
    
    # Analyze losing trades specifically
    print(f"\n💔 LOSING TRADE ANALYSIS:")
    
    if len(losing_trades) > 0:
        losing_trades['short_distance'] = losing_trades['short_strike'] - losing_trades['spx_price']
        losing_trades['short_itm'] = losing_trades['spx_price'] < losing_trades['short_strike']
        
        print(f"   Losing trades with ITM short: {losing_trades['short_itm'].sum()}/{len(losing_trades)} ({losing_trades['short_itm'].mean()*100:.1f}%)")
        print(f"   Avg short distance on losses: {losing_trades['short_distance'].mean():.0f} points")
        print(f"   Avg contracts on losses: {losing_trades['contracts'].mean():.1f}")
        print(f"   Avg credit on losses: ${losing_trades['net_credit'].mean():.2f}")
        
        # Show worst losing trades
        worst_trades = losing_trades.nsmallest(5, 'net_pnl')
        print(f"\n   TOP 5 WORST TRADES:")
        for i, (_, trade) in enumerate(worst_trades.iterrows(), 1):
            itm_status = "ITM" if trade['spx_price'] < trade['short_strike'] else "OTM"
            print(f"   {i}. ${trade['net_pnl']:,.0f} - SPX {trade['spx_price']:.0f}, Short {trade['short_strike']:.0f} ({itm_status}), {trade['contracts']} contracts")
    
    # Position sizing analysis
    print(f"\n📦 POSITION SIZING ANALYSIS:")
    print(f"   Avg contracts: {trades_df['contracts'].mean():.1f}")
    print(f"   Min contracts: {trades_df['contracts'].min()}")
    print(f"   Max contracts: {trades_df['contracts'].max()}")
    
    # Confidence vs performance
    print(f"\n🎯 CONFIDENCE vs PERFORMANCE:")
    
    # Group by confidence ranges
    trades_df['conf_bucket'] = pd.cut(trades_df['confidence_score'], bins=5)
    conf_analysis = trades_df.groupby('conf_bucket').agg({
        'net_pnl': ['count', 'sum', 'mean'],
        'win_loss_flag': 'mean',
        'contracts': 'mean'
    }).round(2)
    
    print("   Confidence Range | Trades | Total P&L | Avg P&L | Win Rate | Avg Contracts")
    print("   " + "-" * 75)
    for conf_range in conf_analysis.index:
        if pd.isna(conf_range):
            continue
        trade_count = conf_analysis.loc[conf_range, ('net_pnl', 'count')]
        total_pnl = conf_analysis.loc[conf_range, ('net_pnl', 'sum')]
        avg_pnl = conf_analysis.loc[conf_range, ('net_pnl', 'mean')]
        win_rate = conf_analysis.loc[conf_range, ('win_loss_flag', 'mean')] * 100
        avg_contracts = conf_analysis.loc[conf_range, ('contracts', 'mean')]
        
        print(f"   {str(conf_range):<16} | {trade_count:>6} | ${total_pnl:>8,.0f} | ${avg_pnl:>7,.0f} | {win_rate:>7.1f}% | {avg_contracts:>12.1f}")
    
    # Credit analysis
    print(f"\n💰 CREDIT ANALYSIS:")
    print(f"   Avg credit: ${trades_df['net_credit'].mean():.2f}")
    print(f"   Min credit: ${trades_df['net_credit'].min():.2f}")
    print(f"   Max credit: ${trades_df['net_credit'].max():.2f}")
    
    # Credit vs performance
    trades_df['credit_bucket'] = pd.cut(trades_df['net_credit'], bins=5)
    credit_analysis = trades_df.groupby('credit_bucket').agg({
        'net_pnl': ['count', 'sum', 'mean'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    print("\n   Credit Range | Trades | Total P&L | Avg P&L | Win Rate")
    print("   " + "-" * 55)
    for credit_range in credit_analysis.index:
        if pd.isna(credit_range):
            continue
        trade_count = credit_analysis.loc[credit_range, ('net_pnl', 'count')]
        total_pnl = credit_analysis.loc[credit_range, ('net_pnl', 'sum')]
        avg_pnl = credit_analysis.loc[credit_range, ('net_pnl', 'mean')]
        win_rate = credit_analysis.loc[credit_range, ('win_loss_flag', 'mean')] * 100
        
        print(f"   {str(credit_range):<12} | {trade_count:>6} | ${total_pnl:>8,.0f} | ${avg_pnl:>7,.0f} | {win_rate:>7.1f}%")
    
    # Identify key issues
    print(f"\n🚨 KEY ISSUES IDENTIFIED:")
    
    issues = []
    
    # Issue 1: Strike selection
    avg_short_distance = trades_df['short_distance'].mean()
    if avg_short_distance < 50:
        issues.append(f"SHORT STRIKES TOO CLOSE: Avg {avg_short_distance:.0f} points from SPX")
    
    # Issue 2: ITM rate
    itm_rate = trades_df['short_itm'].mean() * 100
    if itm_rate > 30:
        issues.append(f"HIGH ITM RATE: {itm_rate:.1f}% of trades have SPX below short strike")
    
    # Issue 3: Position sizing
    avg_contracts = trades_df['contracts'].mean()
    if avg_contracts > 20:
        issues.append(f"POSITION SIZES TOO LARGE: Avg {avg_contracts:.1f} contracts")
    
    # Issue 4: Win rate
    if win_rate < 60:
        issues.append(f"LOW WIN RATE: {win_rate:.1f}% (target >70%)")
    
    # Issue 5: Credit quality
    low_credit_trades = trades_df[trades_df['net_credit'] < 50].shape[0]
    if low_credit_trades > len(trades_df) * 0.3:
        issues.append(f"LOW CREDIT TRADES: {low_credit_trades} trades with <$50 credit")
    
    for i, issue in enumerate(issues, 1):
        print(f"   {i}. {issue}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDED FIXES:")
    
    fixes = []
    
    if avg_short_distance < 50:
        fixes.append("Move short strikes further OTM (100+ points from SPX)")
    
    if itm_rate > 30:
        fixes.append("Improve strike selection to reduce ITM assignments")
    
    if avg_contracts > 15:
        fixes.append("Reduce position sizes (target 5-15 contracts)")
    
    if win_rate < 60:
        fixes.append("Tighten signal filters to improve trade quality")
    
    if len(losing_trades) > 0 and losing_trades['net_pnl'].mean() < -5000:
        fixes.append("Implement stop-loss or better exit criteria")
    
    for i, fix in enumerate(fixes, 1):
        print(f"   {i}. {fix}")
    
    return trades_df

if __name__ == "__main__":
    analyze_current_drawdown()
