"""
Specialized Options Trading Logger for JPM Strategies
Captures complete options chain data for signal, entry, and exit events
"""
import pandas as pd
import numpy as np
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

from config.constants import *

class OptionsLogger:
    """Specialized logger for options trading events"""
    
    def __init__(self, strategy_name: str):
        self.strategy_name = strategy_name
        self.log_filename = self._get_log_filename()
        self.trade_events = []
        
        # Initialize CSV file with headers
        self._initialize_log_file()
        
        # Setup standard logging
        self.logger = self._setup_standard_logger()
    
    def _get_log_filename(self) -> str:
        """Generate log filename"""
        date_str = datetime.now().strftime(LOG_DATE_FORMAT)
        return f"{LOGS_DIR}/options_trading_log_{self.strategy_name.lower().replace(' ', '_')}_{date_str}.csv"
    
    def _initialize_log_file(self):
        """Initialize CSV log file with headers"""
        os.makedirs(LOGS_DIR, exist_ok=True)
        
        # Create header row
        header_df = pd.DataFrame(columns=OPTIONS_LOG_COLUMNS)
        header_df.to_csv(self.log_filename, index=False)
    
    def _setup_standard_logger(self) -> logging.Logger:
        """Setup standard Python logger"""
        logger = logging.getLogger(f"options_trading_{self.strategy_name}")
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        # Create file handler
        log_file = f"{LOGS_DIR}/{self.strategy_name.lower().replace(' ', '_')}.log"
        handler = logging.FileHandler(log_file)
        handler.setLevel(getattr(logging, LOG_LEVEL))
        
        # Create formatter
        formatter = logging.Formatter(LOG_FORMAT)
        handler.setFormatter(formatter)
        
        # Add handler to logger
        if not logger.handlers:
            logger.addHandler(handler)
        
        return logger
    
    def log_signal_event(self, 
                        date: datetime, 
                        options_chain: pd.DataFrame,
                        signal_direction: str,
                        vix_level: float,
                        vrp_value: float,
                        confidence_score: float):
        """Log complete options chain data for signal generation"""
        try:
            for _, option in options_chain.iterrows():
                event_data = {
                    'date': date.strftime(DATE_FORMAT),
                    'event_type': EVENT_SIGNAL,
                    'option_symbol': f"SPX{option.get('expiry', '').strftime('%y%m%d') if pd.notna(option.get('expiry')) else ''}{'C' if option.get('Call/Put') == 'c' else 'P'}{int(option.get('Strike', 0))}",
                    'strike': option.get('Strike', 0),
                    'expiry': option.get('expiry', '').strftime(DATE_FORMAT) if pd.notna(option.get('expiry')) else '',
                    'price': option.get('price', 0),
                    'bid': option.get('bid', 0),
                    'ask': option.get('ask', 0),
                    'volume': option.get('Volume', 0),
                    'open_interest': option.get('Open Interest', 0),
                    'delta': option.get('Delta', 0),
                    'gamma': option.get('Gamma', 0),
                    'theta': option.get('Theta', 0),
                    'vega': option.get('Vega', 0),
                    'underlying_price': option.get('spx_close', 0),
                    'days_to_expiry': option.get('days_to_expiry', 0),
                    'moneyness': option.get('Strike', 0) / option.get('spx_close', 1) if option.get('spx_close', 0) > 0 else 0,
                    'signal_direction': signal_direction
                }
                self.trade_events.append(event_data)
            
            # Log summary to standard logger
            self.logger.info(f"SIGNAL - {signal_direction} signal generated on {date.strftime(DATE_FORMAT)} "
                           f"(VIX: {vix_level:.2f}, VRP: {vrp_value:.2f}, Confidence: {confidence_score:.2f}) "
                           f"- {len(options_chain)} options logged")
            
        except Exception as e:
            self.logger.error(f"Error logging signal event: {e}")
    
    def log_entry_event(self, 
                       date: datetime,
                       selected_options: List[pd.Series],
                       position_size: int,
                       strategy_type: str):
        """Log exact options used for trade entry"""
        try:
            for i, option in enumerate(selected_options):
                leg_type = "SHORT" if i == 0 and strategy_type == "call_spread" else "LONG"
                
                event_data = {
                    'date': date.strftime(DATE_FORMAT),
                    'event_type': f"{EVENT_ENTRY}_{leg_type}",
                    'option_symbol': f"SPX{option.get('expiry', '').strftime('%y%m%d') if pd.notna(option.get('expiry')) else ''}{'C' if option.get('Call/Put') == 'c' else 'P'}{int(option.get('Strike', 0))}",
                    'strike': option.get('Strike', 0),
                    'expiry': option.get('expiry', '').strftime(DATE_FORMAT) if pd.notna(option.get('expiry')) else '',
                    'price': option.get('price', 0),
                    'bid': option.get('bid', 0),
                    'ask': option.get('ask', 0),
                    'volume': option.get('Volume', 0),
                    'open_interest': option.get('Open Interest', 0),
                    'delta': option.get('Delta', 0),
                    'gamma': option.get('Gamma', 0),
                    'theta': option.get('Theta', 0),
                    'vega': option.get('Vega', 0),
                    'underlying_price': option.get('spx_close', 0),
                    'days_to_expiry': option.get('days_to_expiry', 0),
                    'moneyness': option.get('Strike', 0) / option.get('spx_close', 1) if option.get('spx_close', 0) > 0 else 0,
                    'signal_direction': f"{strategy_type.upper()}_{leg_type}"
                }
                self.trade_events.append(event_data)
            
            # Log summary to standard logger
            strikes = [opt.get('Strike', 0) for opt in selected_options]
            prices = [opt.get('price', 0) for opt in selected_options]
            self.logger.info(f"ENTRY - {strategy_type} position entered on {date.strftime(DATE_FORMAT)} "
                           f"(Strikes: {strikes}, Prices: {prices}, Size: {position_size} contracts)")
            
        except Exception as e:
            self.logger.error(f"Error logging entry event: {e}")
    
    def log_exit_event(self, 
                      date: datetime,
                      exit_options: List[pd.Series],
                      final_pnl: float,
                      strategy_type: str):
        """Log exact options used for trade exit"""
        try:
            for i, option in enumerate(exit_options):
                leg_type = "SHORT" if i == 0 and strategy_type == "call_spread" else "LONG"
                
                event_data = {
                    'date': date.strftime(DATE_FORMAT),
                    'event_type': f"{EVENT_EXIT}_{leg_type}",
                    'option_symbol': f"SPX{option.get('expiry', '').strftime('%y%m%d') if pd.notna(option.get('expiry')) else ''}{'C' if option.get('Call/Put') == 'c' else 'P'}{int(option.get('Strike', 0))}",
                    'strike': option.get('Strike', 0),
                    'expiry': option.get('expiry', '').strftime(DATE_FORMAT) if pd.notna(option.get('expiry')) else '',
                    'price': option.get('price', 0),
                    'bid': option.get('bid', 0),
                    'ask': option.get('ask', 0),
                    'volume': option.get('Volume', 0),
                    'open_interest': option.get('Open Interest', 0),
                    'delta': option.get('Delta', 0),
                    'gamma': option.get('Gamma', 0),
                    'theta': option.get('Theta', 0),
                    'vega': option.get('Vega', 0),
                    'underlying_price': option.get('spx_close', 0),
                    'days_to_expiry': option.get('days_to_expiry', 0),
                    'moneyness': option.get('Strike', 0) / option.get('spx_close', 1) if option.get('spx_close', 0) > 0 else 0,
                    'signal_direction': f"{strategy_type.upper()}_{leg_type}_EXIT"
                }
                self.trade_events.append(event_data)
            
            # Log summary to standard logger
            strikes = [opt.get('Strike', 0) for opt in exit_options]
            prices = [opt.get('price', 0) for opt in exit_options]
            self.logger.info(f"EXIT - {strategy_type} position closed on {date.strftime(DATE_FORMAT)} "
                           f"(Strikes: {strikes}, Prices: {prices}, P&L: ${final_pnl:,.2f})")
            
        except Exception as e:
            self.logger.error(f"Error logging exit event: {e}")
    
    def flush_to_csv(self):
        """Write all accumulated events to CSV file"""
        try:
            if self.trade_events:
                events_df = pd.DataFrame(self.trade_events)
                
                # Append to existing file
                events_df.to_csv(self.log_filename, mode='a', header=False, index=False)
                
                # Clear events buffer
                self.trade_events = []
                
                self.logger.info(f"Flushed {len(events_df)} events to {self.log_filename}")
            
        except Exception as e:
            self.logger.error(f"Error flushing events to CSV: {e}")
    
    def log_performance_summary(self, performance_metrics: Dict[str, Any]):
        """Log final performance summary"""
        self.logger.info("=" * 60)
        self.logger.info(f"PERFORMANCE SUMMARY - {self.strategy_name}")
        self.logger.info("=" * 60)
        
        for metric, value in performance_metrics.items():
            if isinstance(value, float):
                self.logger.info(f"{metric}: {value:.2f}")
            else:
                self.logger.info(f"{metric}: {value}")
        
        self.logger.info("=" * 60)
    
    def close(self):
        """Close logger and flush any remaining events"""
        self.flush_to_csv()
        
        # Close handlers
        for handler in self.logger.handlers:
            handler.close()
            self.logger.removeHandler(handler)
