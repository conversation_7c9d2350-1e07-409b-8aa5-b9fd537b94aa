"""
Test Data Loader Performance
Verify that the new data loader produces identical results to original strategies
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime

from core.data_loader import DataLoader
from config.constants import *

def test_data_loader_vs_original():
    """Test that data loader produces identical results to original strategy"""
    print("🧪 TESTING DATA LOADER PERFORMANCE")
    print("=" * 60)
    
    # Test 1: Load data with new data loader
    print("📊 Test 1: Loading data with new DataLoader...")
    data_loader = DataLoader()
    new_market_data = data_loader.load_market_data_with_real_vrp()
    new_options_data = data_loader.get_spx_options_data()
    
    if new_market_data is None or new_options_data is None:
        print("❌ New data loader failed to load data")
        return False
    
    print(f"✅ New DataLoader results:")
    print(f"   Market data: {len(new_market_data)} records")
    print(f"   Options data: {len(new_options_data):,} records")
    print(f"   VRP range: {new_market_data['vrp_avg'].min():.2f} to {new_market_data['vrp_avg'].max():.2f}")
    
    # Test 2: Load data with original strategy
    print("\n📊 Test 2: Loading data with original strategy...")
    try:
        from final_strategy_clean import FinalRealDataStrategy
        original_strategy = FinalRealDataStrategy()
        original_market_data = original_strategy.load_market_data_with_real_vrp()
        original_options_data = original_strategy.spx_options_data
        
        if original_market_data is None or original_options_data is None:
            print("❌ Original strategy failed to load data")
            return False
        
        print(f"✅ Original strategy results:")
        print(f"   Market data: {len(original_market_data)} records")
        print(f"   Options data: {len(original_options_data):,} records")
        print(f"   VRP range: {original_market_data['vrp_avg'].min():.2f} to {original_market_data['vrp_avg'].max():.2f}")
        
    except Exception as e:
        print(f"❌ Error loading original strategy data: {e}")
        return False
    
    # Test 3: Compare results
    print("\n🔍 Test 3: Comparing results...")
    
    # Compare market data shapes
    if len(new_market_data) != len(original_market_data):
        print(f"❌ Market data length mismatch: {len(new_market_data)} vs {len(original_market_data)}")
        return False
    
    # Compare options data shapes
    if len(new_options_data) != len(original_options_data):
        print(f"❌ Options data length mismatch: {len(new_options_data)} vs {len(original_options_data)}")
        return False
    
    # Compare VRP calculations (allow small floating point differences)
    vrp_diff = abs(new_market_data['vrp_avg'].mean() - original_market_data['vrp_avg'].mean())
    if vrp_diff > PERFORMANCE_TOLERANCE:
        print(f"❌ VRP calculation mismatch: difference {vrp_diff:.6f}")
        return False
    
    # Compare VIX data
    vix_diff = abs(new_market_data['vix'].mean() - original_market_data['vix'].mean())
    if vix_diff > PERFORMANCE_TOLERANCE:
        print(f"❌ VIX data mismatch: difference {vix_diff:.6f}")
        return False
    
    print("✅ All data comparisons passed!")
    print(f"   VRP difference: {vrp_diff:.6f} (tolerance: {PERFORMANCE_TOLERANCE})")
    print(f"   VIX difference: {vix_diff:.6f} (tolerance: {PERFORMANCE_TOLERANCE})")
    
    # Test 4: Verify data quality
    print("\n🔍 Test 4: Verifying data quality...")
    
    # Check for required columns
    required_market_columns = ['vix', 'vix9d', 'vrp_avg', 'vix_momentum_direction']
    missing_market_cols = [col for col in required_market_columns if col not in new_market_data.columns]
    if missing_market_cols:
        print(f"❌ Missing market data columns: {missing_market_cols}")
        return False
    
    required_options_columns = ['date', 'Strike', 'Call/Put', 'price', 'expiry', 'spx_close']
    missing_options_cols = [col for col in required_options_columns if col not in new_options_data.columns]
    if missing_options_cols:
        print(f"❌ Missing options data columns: {missing_options_cols}")
        return False
    
    # Check data ranges
    if new_market_data['vix'].min() < 5 or new_market_data['vix'].max() > 100:
        print(f"❌ VIX data out of reasonable range: {new_market_data['vix'].min():.2f} - {new_market_data['vix'].max():.2f}")
        return False
    
    if new_options_data['price'].min() < 0 or new_options_data['price'].max() > 1000:
        print(f"❌ Option prices out of reasonable range: {new_options_data['price'].min():.2f} - {new_options_data['price'].max():.2f}")
        return False
    
    print("✅ All data quality checks passed!")
    
    print("\n🎉 DATA LOADER TEST PASSED!")
    print("=" * 60)
    print("✅ New DataLoader produces identical results to original strategy")
    print("✅ All performance benchmarks maintained")
    print("✅ Data quality verified")
    
    return True

def test_options_filtering():
    """Test that options filtering works correctly"""
    print("\n🧪 TESTING OPTIONS FILTERING")
    print("=" * 40)
    
    data_loader = DataLoader()
    options_data = data_loader.load_real_spx_options_data()
    
    if options_data is None:
        print("❌ Failed to load options data")
        return False
    
    # Test date filtering
    test_date = pd.to_datetime('2023-06-15')
    day_options = data_loader.get_options_for_date(test_date, 'c')
    
    if len(day_options) == 0:
        print(f"⚠️ No options found for {test_date} (this may be normal)")
    else:
        print(f"✅ Found {len(day_options)} call options for {test_date}")
        
        # Verify all options are for the correct date
        if not all(day_options['date'] == test_date):
            print("❌ Date filtering failed - found options for wrong dates")
            return False
        
        # Verify all options are calls
        if not all(day_options['Call/Put'] == 'c'):
            print("❌ Option type filtering failed - found non-call options")
            return False
    
    print("✅ Options filtering test passed!")
    return True

if __name__ == "__main__":
    success = test_data_loader_vs_original()
    if success:
        success = test_options_filtering()
    
    if success:
        print("\n🎉 ALL TESTS PASSED - DATA LOADER READY FOR PRODUCTION!")
        exit(0)
    else:
        print("\n❌ TESTS FAILED - DO NOT PROCEED WITH REFACTORING")
        exit(1)
