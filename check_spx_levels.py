#!/usr/bin/env python3
"""
Check SPX levels in the data to optimize the SPX filter
"""

import pandas as pd
import numpy as np

def check_spx_levels():
    """Check SPX price distribution in the data"""
    
    print("📊 CHECKING SPX LEVELS IN DATA")
    print("=" * 40)
    
    # Load trades data to see SPX levels
    try:
        trades_df = pd.read_csv('trades/call_spread_trades.csv')
        print(f"✅ Loaded {len(trades_df)} historical trades")
        
        spx_levels = trades_df['spx_price'].values
        
        print(f"\n📈 SPX PRICE DISTRIBUTION:")
        print(f"   Min SPX: {spx_levels.min():.0f}")
        print(f"   Max SPX: {spx_levels.max():.0f}")
        print(f"   Mean SPX: {spx_levels.mean():.0f}")
        print(f"   Median SPX: {np.median(spx_levels):.0f}")
        
        # Check distribution by ranges
        ranges = [
            (0, 4500, "<4500"),
            (4500, 5000, "4500-5000"),
            (5000, 5500, "5000-5500"),
            (5500, 6000, "5500-6000"),
            (6000, 6500, "6000-6500"),
            (6500, 10000, ">6500")
        ]
        
        print(f"\n📊 SPX RANGE DISTRIBUTION:")
        for min_val, max_val, label in ranges:
            count = len(trades_df[(trades_df['spx_price'] >= min_val) & (trades_df['spx_price'] < max_val)])
            pct = count / len(trades_df) * 100
            print(f"   {label}: {count} trades ({pct:.1f}%)")
        
        # Check performance by SPX level
        print(f"\n💰 PERFORMANCE BY SPX LEVEL:")
        for min_val, max_val, label in ranges:
            subset = trades_df[(trades_df['spx_price'] >= min_val) & (trades_df['spx_price'] < max_val)]
            if len(subset) > 0:
                win_rate = subset['win_loss_flag'].mean() * 100
                avg_pnl = subset['net_pnl'].mean()
                total_pnl = subset['net_pnl'].sum()
                print(f"   {label}: {win_rate:.1f}% win rate, ${avg_pnl:,.0f} avg, ${total_pnl:,.0f} total")
        
        # Suggest optimal SPX threshold
        print(f"\n🎯 OPTIMAL SPX THRESHOLD ANALYSIS:")
        
        # Test different thresholds
        thresholds = [4500, 5000, 5500, 5800, 6000, 6200]
        
        for threshold in thresholds:
            above_threshold = trades_df[trades_df['spx_price'] >= threshold]
            if len(above_threshold) > 0:
                win_rate = above_threshold['win_loss_flag'].mean() * 100
                trade_count = len(above_threshold)
                total_pnl = above_threshold['net_pnl'].sum()
                print(f"   SPX ≥ {threshold}: {trade_count} trades, {win_rate:.1f}% win rate, ${total_pnl:,.0f} total")
        
    except FileNotFoundError:
        print("❌ No historical trades file found")
        return
    
    # Also check current new trades
    try:
        new_trades_df = pd.read_csv('trades/call_spread_trades.csv')
        if len(new_trades_df) > 0:
            print(f"\n🆕 NEW STRATEGY RESULTS:")
            print(f"   Trades executed: {len(new_trades_df)}")
            print(f"   SPX levels: {new_trades_df['spx_price'].min():.0f} - {new_trades_df['spx_price'].max():.0f}")
            print(f"   Win rate: {new_trades_df['win_loss_flag'].mean() * 100:.1f}%")
            print(f"   Total P&L: ${new_trades_df['net_pnl'].sum():,.0f}")
    except:
        pass

if __name__ == "__main__":
    check_spx_levels()
