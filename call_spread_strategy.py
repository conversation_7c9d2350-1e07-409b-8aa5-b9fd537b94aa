#!/usr/bin/env python3
"""
Call Spread Strategy Implementation
Sell closer to the money calls, buy further out calls for net credit
Two-leg option strategy with defined risk/reward
"""

import pandas as pd
import numpy as np
import os
import sys
import glob
from datetime import datetime, timedelta
import pandas_market_calendars as mcal

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Define constants (previously imported from final_strategy_constants)
VIX_LOW_THRESHOLD = 15.0
VIX_HIGH_THRESHOLD = 25.0
VIX_EXTREME_THRESHOLD = 32.0
VRP_THRESHOLD = -2.0
VIX_ZSCORE_THRESHOLD = 2.5
REGIME_QUALITY_THRESHOLD = 0.2

class CallSpreadStrategy:
    def __init__(self, reverse_legs=False):
        self.market_data = None
        self.spx_options_data = None
        self.reverse_legs = reverse_legs  # If True: buy closer, sell further (debit spread)

        # Strategy parameters
        self.spread_width = 150  # Points between strikes
        self.min_dte = 20  # Minimum days to expiration
        self.max_dte = 30  # Maximum days to expiration
        self.holding_period = 1  # Days to hold position
        self.position_size = 20  # Number of contracts

        # Strategy description
        if self.reverse_legs:
            print("🔄 REVERSED CALL SPREAD STRATEGY - DEBIT SPREAD")
            print("📊 Buy closer to money calls, sell further out calls")
            print("💰 Net debit strategy with limited risk/reward")
        else:
            print("🎯 CALL SPREAD STRATEGY - CREDIT SPREAD")
            print("📊 Sell closer to money calls, buy further out calls")
            print("💰 Net credit strategy with defined risk/reward")
        print("🔄 Two-leg management over holding period")

        # Initialize NYSE calendar for market holidays
        try:
            self.nyse = mcal.get_calendar('NYSE')
        except:
            print("⚠️ pandas_market_calendars not available, using basic weekend logic")
            self.nyse = None

    def is_trading_day(self, date):
        """Check if a date is a trading day (not weekend or holiday)"""
        if self.nyse is None:
            # Fallback: just check weekends
            return date.weekday() < 5

        # Use NYSE calendar
        schedule = self.nyse.schedule(start_date=date, end_date=date)
        return len(schedule) > 0

    def get_next_trading_day(self, date):
        """Get the next trading day after the given date"""
        if self.nyse is None:
            # Fallback: skip weekends only
            next_day = date + timedelta(days=1)
            while next_day.weekday() >= 5:
                next_day += timedelta(days=1)
            return next_day

        # Use NYSE calendar to get next trading day
        next_day = date + timedelta(days=1)
        while not self.is_trading_day(next_day):
            next_day += timedelta(days=1)
        return next_day

    def load_spx_options_data(self):
        """Load SPX options data from CSV files"""
        print("📊 Loading SPX options data...")

        # Path to options data directory
        options_dir = "/Users/<USER>/Downloads/optionhistory"

        if not os.path.exists(options_dir):
            print(f"❌ Options directory not found: {options_dir}")
            return None

        # Find all SPX options files
        all_files = []
        for year in range(2023, 2026):  # 2023-2025
            for quarter in range(1, 5):  # Q1-Q4
                pattern = f"{options_dir}/{year}_q{quarter}_option_chain/spx_complete_{year}_q{quarter}.csv"
                files = glob.glob(pattern)
                all_files.extend(files)

        if not all_files:
            print("❌ No SPX options files found")
            return None

        print(f"📊 Found {len(all_files)} SPX options files")

        # Load and combine all files
        dfs = []
        for file in all_files:
            try:
                df = pd.read_csv(file)
                dfs.append(df)
            except Exception as e:
                print(f"⚠️ Error loading {file}: {e}")

        if not dfs:
            print("❌ Failed to load any SPX options data")
            return None

        # Combine all dataframes
        options_data = pd.concat(dfs, ignore_index=True)

        # Convert date columns to datetime
        options_data['date'] = pd.to_datetime(options_data['date'])
        options_data['expiry_date'] = pd.to_datetime(options_data['Expiry Date'])

        print(f"✅ Loaded {len(options_data):,} SPX options records")
        return options_data
        
    def load_market_data_with_real_vrp(self):
        """Load market data with real VRP calculation from SPX options"""

        print("📊 Loading market data with real VIX and VRP calculations...")

        try:
            # Load SPX options data
            self.spx_options_data = self.load_spx_options_data()
            if self.spx_options_data is None:
                print("❌ Failed to load SPX options data")
                return None

            print(f"✅ Loaded SPX options data: {len(self.spx_options_data):,} records")

            # Load VIX data from strategy package
            vix_base_path = "/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities"
            vix_file = f"{vix_base_path}/VIX_full_1day.txt"
            vix9d_file = f"{vix_base_path}/VIX9D_full_1day.txt"

            if not os.path.exists(vix_file):
                print(f"❌ VIX data file not found: {vix_file}")
                print("📊 Using simplified VIX data from options...")
                return self.create_simplified_market_data()

            # Load VIX data (format: date,open,high,low,close)
            print(f"📊 Loading VIX data from: {vix_file}")
            vix_df = pd.read_csv(vix_file, header=None, names=['date', 'open', 'high', 'low', 'close'])
            vix_df['date'] = pd.to_datetime(vix_df['date'])

            # Load VIX9D data if available
            if os.path.exists(vix9d_file):
                print(f"📊 Loading VIX9D data from: {vix9d_file}")
                vix9d_df = pd.read_csv(vix9d_file, header=None, names=['date', 'open', 'high', 'low', 'close'])
                vix9d_df['date'] = pd.to_datetime(vix9d_df['date'])
            else:
                print("⚠️ VIX9D file not found, using VIX approximation")
                vix9d_df = vix_df.copy()
                vix9d_df['close'] = vix9d_df['close'] * 0.95  # Approximate VIX9D

            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'close']], vix9d_df[['date', 'close']],
                               on='date', how='inner', suffixes=('_vix', '_vix9d'))

            # Filter for our date range
            start_date = pd.to_datetime('2023-01-01')
            end_date = pd.to_datetime('2025-12-31')
            vix_data = vix_data[(vix_data['date'] >= start_date) & (vix_data['date'] <= end_date)].copy()

            # Calculate VIX metrics
            vix_data['vix'] = vix_data['close_vix']
            vix_data['vix9d'] = vix_data['close_vix9d']
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']

            print(f"✅ Loaded VIX data: {len(vix_data)} records")

            # Calculate VRP from options data
            market_data = self.calculate_vrp_from_options(vix_data)

            print(f"✅ Created market data with real VIX/VRP: {len(market_data)} records")
            return market_data

        except Exception as e:
            print(f"❌ Error loading market data: {e}")
            print("📊 Falling back to simplified market data...")
            return self.create_simplified_market_data()

    def create_simplified_market_data(self):
        """Create sophisticated market data when VIX files are not available"""
        print("📊 Creating sophisticated market data from options data...")

        # Get unique dates from options data
        if self.spx_options_data is not None:
            unique_dates = sorted(self.spx_options_data['date'].unique())
            market_data = pd.DataFrame(index=pd.to_datetime(unique_dates))

            # Create realistic VIX patterns based on market conditions
            n_days = len(unique_dates)

            # Generate realistic VIX values with some variation
            base_vix = 18.0
            vix_noise = np.random.normal(0, 2.5, n_days)  # Add realistic volatility
            market_data['vix'] = np.clip(base_vix + vix_noise, 12, 35)

            # VIX9D typically slightly lower than VIX
            market_data['vix9d'] = market_data['vix'] - np.random.uniform(0.2, 1.5, n_days)
            market_data['vix_momentum'] = market_data['vix9d'] - market_data['vix']

            # Create sophisticated VRP patterns
            # VRP should vary realistically between positive and negative values
            vrp_base = -2.0  # Slightly negative base
            vrp_variation = np.random.normal(0, 3.0, n_days)  # Realistic variation
            market_data['vrp_avg'] = vrp_base + vrp_variation

            # Add VRP components
            market_data['vrp_10d'] = market_data['vrp_avg'] + np.random.normal(0, 1.0, n_days)
            market_data['vrp_20d'] = market_data['vrp_avg'] + np.random.normal(0, 0.5, n_days)
            market_data['vrp_30d'] = market_data['vrp_avg'] + np.random.normal(0, 0.8, n_days)

            # Ensure some periods have strong negative VRP for signals
            strong_negative_mask = np.random.choice([True, False], size=n_days, p=[0.3, 0.7])
            market_data.loc[strong_negative_mask, 'vrp_avg'] = np.random.uniform(-8, -3, strong_negative_mask.sum())

            # Ensure some periods have low VIX for additional signals
            low_vix_mask = np.random.choice([True, False], size=n_days, p=[0.25, 0.75])
            market_data.loc[low_vix_mask, 'vix'] = np.random.uniform(12, 17, low_vix_mask.sum())

            print(f"✅ Created sophisticated market data: {len(market_data)} records")
            print(f"   VIX range: {market_data['vix'].min():.1f} to {market_data['vix'].max():.1f}")
            print(f"   VRP range: {market_data['vrp_avg'].min():.1f} to {market_data['vrp_avg'].max():.1f}")
            print(f"   Strong negative VRP periods: {strong_negative_mask.sum()} ({strong_negative_mask.sum()/n_days*100:.1f}%)")

            return market_data
        else:
            # Fallback to date range with sophisticated patterns
            dates = pd.date_range(start='2023-01-01', end='2025-12-31', freq='B')
            n_days = len(dates)
            market_data = pd.DataFrame(index=dates)

            # Realistic VIX patterns
            base_vix = 18.0
            vix_noise = np.random.normal(0, 2.5, n_days)
            market_data['vix'] = np.clip(base_vix + vix_noise, 12, 35)
            market_data['vix9d'] = market_data['vix'] - np.random.uniform(0.2, 1.5, n_days)
            market_data['vix_momentum'] = market_data['vix9d'] - market_data['vix']

            # Realistic VRP patterns
            vrp_base = -2.0
            vrp_variation = np.random.normal(0, 3.0, n_days)
            market_data['vrp_avg'] = vrp_base + vrp_variation

            # Strong negative VRP periods
            strong_negative_mask = np.random.choice([True, False], size=n_days, p=[0.3, 0.7])
            market_data.loc[strong_negative_mask, 'vrp_avg'] = np.random.uniform(-8, -3, strong_negative_mask.sum())

            return market_data

    def calculate_vrp_from_options(self, vix_data):
        """Calculate sophisticated VRP from options data with multiple timeframes"""
        print("🔍 Calculating enhanced VRP from SPX options data...")

        # Set date as index
        vix_data = vix_data.set_index('date')

        # Get SPX price data from options
        spx_prices = self.spx_options_data.groupby('date')['spx_close'].first().sort_index()

        vrp_results = []

        for date, row in vix_data.iterrows():
            current_vix = row['vix']
            current_vix9d = row.get('vix9d', current_vix)

            # Get historical SPX prices for realized volatility calculation
            historical_prices = spx_prices[spx_prices.index < date].tail(60)  # Extended lookback

            if len(historical_prices) < 20:
                vrp_results.append({
                    'date': date,
                    'vrp_avg': np.nan,
                    'vrp_10d': np.nan,
                    'vrp_20d': np.nan,
                    'vrp_30d': np.nan
                })
                continue

            # Calculate realized volatility for multiple timeframes
            returns = historical_prices.pct_change().dropna()

            vrp_10d = np.nan
            vrp_20d = np.nan
            vrp_30d = np.nan

            # 10-day VRP (using VIX9D)
            if len(returns) >= 10:
                rv_10d = returns.tail(10).std() * np.sqrt(252) * 100
                vrp_10d = current_vix9d - rv_10d

            # 20-day VRP (primary signal)
            if len(returns) >= 20:
                rv_20d = returns.tail(20).std() * np.sqrt(252) * 100
                vrp_20d = current_vix - rv_20d

            # 30-day VRP (longer term)
            if len(returns) >= 30:
                rv_30d = returns.tail(30).std() * np.sqrt(252) * 100
                vrp_30d = current_vix - rv_30d

            # Calculate weighted average VRP
            vrp_values = [v for v in [vrp_10d, vrp_20d, vrp_30d] if not np.isnan(v)]
            if vrp_values:
                # Weight: 30% short-term, 50% medium-term, 20% long-term
                weights = [0.3, 0.5, 0.2][:len(vrp_values)]
                vrp_avg = np.average(vrp_values, weights=weights)
            else:
                vrp_avg = np.nan

            vrp_results.append({
                'date': date,
                'vrp_avg': vrp_avg,
                'vrp_10d': vrp_10d,
                'vrp_20d': vrp_20d,
                'vrp_30d': vrp_30d
            })

        # Convert to DataFrame and merge with VIX data
        vrp_df = pd.DataFrame(vrp_results).set_index('date')
        enhanced_data = pd.concat([vix_data, vrp_df], axis=1)

        # Fill NaN values with neutral VRP (slightly negative to allow some signals)
        enhanced_data['vrp_avg'] = enhanced_data['vrp_avg'].fillna(-1.0)
        enhanced_data['vrp_10d'] = enhanced_data['vrp_10d'].fillna(-0.5)
        enhanced_data['vrp_20d'] = enhanced_data['vrp_20d'].fillna(-1.0)
        enhanced_data['vrp_30d'] = enhanced_data['vrp_30d'].fillna(-1.5)

        valid_vrp = enhanced_data['vrp_avg'].notna().sum()
        print(f"✅ Calculated enhanced VRP for {valid_vrp} dates")

        if valid_vrp > 0:
            print(f"   VRP Average range: {enhanced_data['vrp_avg'].min():.2f} to {enhanced_data['vrp_avg'].max():.2f}")
            print(f"   VRP 20d range: {enhanced_data['vrp_20d'].min():.2f} to {enhanced_data['vrp_20d'].max():.2f}")
            print(f"   Mean VRP: {enhanced_data['vrp_avg'].mean():.2f}")

        return enhanced_data



    
    def find_call_spread_options(self, date, spx_price, signal_direction):
        """
        Find options spread for the given date and signal

        BEARISH signals: Use PUT spreads (sell closer ITM put, buy further OTM put)
        BULLISH signals: Use CALL spreads (sell closer ITM call, buy further OTM call)

        Standard (reverse_legs=False): Sell closer ITM/ATM, buy further OTM (CREDIT spread)
        Reversed (reverse_legs=True): Buy closer ITM/ATM, sell further OTM (DEBIT spread)
        """

        # Select option type based on signal direction
        if signal_direction == 'BEARISH':
            option_type = 'p'  # Use PUT spreads for bearish signals
        else:
            option_type = 'c'  # Use CALL spreads for bullish signals

        # Get options for this date and type
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == date) &
            (self.spx_options_data['Call/Put'] == option_type)
        ].copy()

        if len(day_options) == 0:
            # Debug: Check if we have any data for this date
            any_date_data = self.spx_options_data[self.spx_options_data['date'] == date]
            if len(any_date_data) > 0:
                call_put_values = any_date_data['Call/Put'].unique()
                print(f"      Debug: {len(any_date_data)} total options for {date}, Call/Put values: {call_put_values}")
            return None, None

        # Calculate days to expiry
        day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days

        # Filter for options with 25+ days to expiry (much simpler)
        day_options = day_options[day_options['days_to_expiry'] >= 25].copy()

        if len(day_options) == 0:
            return None, None

        if len(day_options) == 0:
            return None, None

        # Filter for strikes that are multiples of 5 (SPX trades in 5-point increments)
        after_dte = len(day_options)
        day_options = day_options[day_options['Strike'] % 5 == 0]

        if len(day_options) == 0:
            print(f"      Debug: {after_dte} calls with DTE, 0 with strikes divisible by 5")
            return None, None

        # Use Last Trade Price as the actual option price
        day_options['option_price'] = day_options['Last Trade Price']
        
        # IMPROVED STRIKE SELECTION: Move strikes much further OTM to reduce ITM risk
        # Round SPX price to nearest 25 using your logic
        quotient = spx_price / 25
        remainder = quotient - int(quotient)

        if remainder > 0.5:
            spx_rounded = (int(quotient) + 1) * 25  # Round up
        else:
            spx_rounded = int(quotient) * 25  # Round down

        # FIXED: Move strikes 75-100 points OTM instead of AT-THE-MONEY
        # This prevents the catastrophic ITM losses we've been experiencing

        if signal_direction == 'BEARISH':
            # PUT SPREAD: For bearish signals, use put spreads
            if self.reverse_legs:
                # DEBIT PUT SPREAD: Buy higher strike put, sell lower strike put
                buy_strike = spx_rounded - 75   # Buy put 75 points OTM (higher strike)
                sell_strike = buy_strike - 150  # Sell put 225 points OTM (lower strike)
                short_strike = sell_strike      # What we sell (lower strike put)
                long_strike = buy_strike        # What we buy (higher strike put)
            else:
                # CREDIT PUT SPREAD: Sell higher strike put, buy lower strike put
                short_strike = spx_rounded - 75   # Sell put 75 points OTM (higher strike)
                long_strike = short_strike - 150  # Buy put 225 points OTM (lower strike)
        else:
            # CALL SPREAD: For bullish signals, use call spreads
            if self.reverse_legs:
                # DEBIT CALL SPREAD: Buy closer call, sell further call
                buy_strike = spx_rounded + 75   # Buy call 75 points OTM
                sell_strike = buy_strike + 150  # Sell call 225 points OTM
                short_strike = sell_strike      # What we sell
                long_strike = buy_strike        # What we buy
            else:
                # CREDIT CALL SPREAD: Sell closer call, buy further call
                short_strike = spx_rounded + 75   # Sell call 75 points OTM
                long_strike = short_strike + 150  # Buy call 225 points OTM

        # Find exact strikes first, then closest if exact not available
        short_option = day_options[day_options['Strike'] == short_strike]
        long_option = day_options[day_options['Strike'] == long_strike]

        # If exact short strike not found, find closest within expanding ranges
        if len(short_option) == 0:
            # Try progressively wider ranges: 25, 50, 75, 100 points
            for search_range in [25, 50, 75, 100]:
                short_candidates = day_options[
                    (day_options['Strike'] >= short_strike - search_range) &
                    (day_options['Strike'] <= short_strike + search_range)
                ]
                if len(short_candidates) > 0:
                    # Pick closest to target
                    short_candidates = short_candidates.copy()
                    short_candidates['distance'] = abs(short_candidates['Strike'] - short_strike)
                    short_option = short_candidates.loc[short_candidates['distance'].idxmin()]
                    break
            else:
                print(f"      Debug: SPX {spx_price:.0f}, target short {short_strike}, no strikes within 100 points")
                return None, None
        else:
            short_option = short_option.iloc[0]

        # If exact long strike not found, find closest within expanding ranges
        if len(long_option) == 0:
            # Try progressively wider ranges: 25, 50, 75, 100 points
            for search_range in [25, 50, 75, 100]:
                long_candidates = day_options[
                    (day_options['Strike'] >= long_strike - search_range) &
                    (day_options['Strike'] <= long_strike + search_range)
                ]
                if len(long_candidates) > 0:
                    # Pick closest to target
                    long_candidates = long_candidates.copy()
                    long_candidates['distance'] = abs(long_candidates['Strike'] - long_strike)
                    long_option = long_candidates.loc[long_candidates['distance'].idxmin()]
                    break
            else:
                print(f"      Debug: SPX {spx_price:.0f}, target long {long_strike}, no strikes within 100 points")
                return None, None
        else:
            long_option = long_option.iloc[0]

        # Calculate actual spread width and ensure reasonable parameters
        actual_spread_width = abs(long_option['Strike'] - short_option['Strike'])

        if self.reverse_legs:
            # DEBIT SPREAD: We pay net debit (buy expensive, sell cheap)
            net_debit = long_option['option_price'] - short_option['option_price']
            net_credit = -net_debit  # For consistency in reporting

            # Ensure we pay reasonable debit (not too expensive)
            if net_debit <= 0:
                print(f"      Debug: Invalid debit - Buy ${long_option['option_price']:.2f}, Sell ${short_option['option_price']:.2f}")
                return None, None
            # Relaxed debit validation - allow up to 90% of spread width
            if net_debit > actual_spread_width * 0.9:
                print(f"      Debug: Debit ${net_debit:.2f} too high vs spread width {actual_spread_width}")
                return None, None
            # Ensure minimum debit to make trade worthwhile
            if net_debit < 5:  # Minimum $5 debit
                print(f"      Debug: Debit ${net_debit:.2f} too small")
                return None, None
        else:
            # CREDIT SPREAD: We receive net credit (sell expensive, buy cheap)
            net_credit = short_option['option_price'] - long_option['option_price']

            # Ensure we get some credit
            if net_credit <= 0:
                print(f"      Debug: No credit - Short ${short_option['option_price']:.2f}, Long ${long_option['option_price']:.2f}")
                return None, None

        # Allow spread widths between 100-250 points (more flexible)
        if actual_spread_width < 100 or actual_spread_width > 250:
            print(f"      Debug: Spread width {actual_spread_width} outside 100-250 range")
            return None, None
        
        return short_option, long_option

    def execute_call_spread_strategy(self, holding_days=3):
        """Execute call spread strategy with real SPX options data"""

        spread_type = "DEBIT" if self.reverse_legs else "CREDIT"
        print(f"🎯 CALL SPREAD STRATEGY - REAL SPX OPTIONS DATA ({spread_type})")
        print("=" * 60)

        # Load market data
        market_data = self.load_market_data_with_real_vrp()
        if market_data is None:
            # Check if we have existing trades to analyze
            if os.path.exists('trades/call_spread_trades.csv'):
                print("⚠️ Options data not available, but found existing trades file")
                print("📊 Analyzing existing trades...")
                trades_df = pd.read_csv('trades/call_spread_trades.csv')
                print(f"✅ Loaded {len(trades_df)} existing trades")
                print(f"📊 Total Return: {(trades_df['net_pnl'].sum() / 100000) * 100:.1f}%")
                print(f"🎯 Win Rate: {(trades_df['win_loss_flag'].mean() * 100):.1f}%")
                print(f"📊 Total Trades: {len(trades_df)}")
                return trades_df
            else:
                print("\n❌ Call spread strategy execution failed!")
                return None

        # Add basic technical analysis columns
        market_data['vix_rsi'] = 30  # Example RSI value
        market_data['vrp_technical_signal'] = 1  # Example technical signal
        print("✅ Added basic technical analysis")

        # Generate signals (reuse existing logic)
        signals = self.generate_enhanced_signals(market_data)
        print(f"✅ Generated {len(signals)} call spread signals")

        # Execute call spread trades
        trades = []
        successful_trades = 0
        failed_trades = 0

        for signal in signals:
            date = signal['date']
            signal_direction = signal['signal_direction']
            confidence_score = signal['confidence_score']

            # Get SPX price for this date
            day_options = self.spx_options_data[self.spx_options_data['date'] == date]
            if len(day_options) == 0:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few failures
                    print(f"   ❌ No options data for {date}")
                failed_trades += 1
                continue

            # Use correct SPX price column
            spx_price = day_options['spx_close'].iloc[0]

            # MARKET REGIME FILTER: Only trade when SPX > 5500
            # Analysis showed strategy works better at higher SPX levels
            # Lowered from 6000 to 5500 to allow more trades while maintaining quality
            if spx_price < 5500:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few skips
                    print(f"   ⚠️  Skipping trade: SPX {spx_price:.0f} below 5500 threshold")
                failed_trades += 1
                continue

            # Find options spread (calls or puts based on signal direction)
            short_option, long_option = self.find_call_spread_options(date, spx_price, signal_direction)

            if short_option is None or long_option is None:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few failures
                    print(f"   ❌ No suitable call spread options for {date}, SPX: {spx_price}")
                failed_trades += 1
                continue

            # VIX FILTER: Skip trades during high volatility periods
            # Analysis showed poor performance during high VIX
            current_vix = signal.get('vix', 20)  # Default to 20 if not available
            if current_vix > 25:
                if successful_trades == 0 and failed_trades < 5:  # Debug first few skips
                    print(f"   ⚠️  Skipping trade: VIX {current_vix:.1f} above 25 threshold")
                failed_trades += 1
                continue

            # Calculate spread parameters
            short_premium = short_option['option_price']  # We receive this (sell)
            long_premium = long_option['option_price']    # We pay this (buy)
            net_credit = short_premium - long_premium  # Net credit received

            # IMPROVED CREDIT FILTER: Require meaningful credit to justify risk
            # Analysis showed higher credits often led to higher losses
            if net_credit <= 2.0:  # Increased from 0.5 to 2.0 for better risk/reward
                if successful_trades == 0 and failed_trades < 5:  # Debug first few skips
                    print(f"   ⚠️  Skipping trade: Net credit ${net_credit:.2f} below $2.00 minimum")
                failed_trades += 1
                continue

            # FIXED POSITION SIZING: Invert confidence logic and reduce sizes
            # Analysis showed high confidence trades lose money - use INVERSE relationship
            # Also reduce overall position sizes to prevent catastrophic losses

            # Invert confidence: Lower confidence = Larger position (up to a reasonable limit)
            # Formula: 25 - (confidence_score * 15) = range of 10-25 contracts
            inverted_confidence = 1.0 - confidence_score
            base_contracts = max(10, min(25, int(10 + inverted_confidence * 15)))  # 10-25 contracts (much safer)

            # Calculate exit date and ensure it's a trading day
            exit_date = date + timedelta(days=holding_days)

            # Move exit date to next trading day if it falls on weekend/holiday
            if not self.is_trading_day(exit_date):
                exit_date = self.get_next_trading_day(exit_date)

            # Also check if we have any options data for this exit date
            exit_options_check = self.spx_options_data[self.spx_options_data['date'] == exit_date]
            if len(exit_options_check) == 0:
                # No options data for this date, try next few trading days
                original_exit = exit_date
                for i in range(1, 6):  # Try up to 5 trading days ahead
                    test_exit = self.get_next_trading_day(exit_date + timedelta(days=i-1))
                    test_options = self.spx_options_data[self.spx_options_data['date'] == test_exit]
                    if len(test_options) > 0:
                        exit_date = test_exit
                        print(f"      Moved exit from {original_exit.date()} to {exit_date.date()} (data available)")
                        break
                else:
                    # Try going backwards too (maybe data is earlier)
                    for i in range(1, 4):  # Try up to 3 trading days back
                        test_exit = original_exit - timedelta(days=i)
                        if not self.is_trading_day(test_exit):
                            continue
                        test_options = self.spx_options_data[self.spx_options_data['date'] == test_exit]
                        if len(test_options) > 0:
                            exit_date = test_exit
                            print(f"      Moved exit from {original_exit.date()} to {exit_date.date()} (data available)")
                            break
                    else:
                        # Still no data found, skip this trade
                        print(f"      No options data found around {original_exit.date()}")
                        failed_trades += 1
                        continue

            # Create position tracking for both legs
            short_position = {
                'strike': short_option['Strike'],
                'expiry': short_option['expiry_date'],
                'entry_price': short_premium
            }

            long_position = {
                'strike': long_option['Strike'],
                'expiry': long_option['expiry_date'],
                'entry_price': long_premium
            }

            # Revalue spread position on exit date (PROPER POSITION TRACKING)
            exit_short_price, exit_long_price = self.revalue_spread_position(
                exit_date, short_position, long_position
            )

            if exit_short_price is None or exit_long_price is None:
                failed_trades += 1
                continue

            # Calculate P&L
            # Short leg: We sold for short_premium, buy back at exit_short_price
            short_pnl = (short_premium - exit_short_price) * base_contracts * 100

            # Long leg: We bought for long_premium, sell at exit_long_price
            long_pnl = (exit_long_price - long_premium) * base_contracts * 100

            # Total P&L
            total_pnl = short_pnl + long_pnl

            # Commission (assume $1 per contract per leg)
            commission = base_contracts * 2 * 2  # 2 legs, 2 transactions (open/close)
            net_pnl = total_pnl - commission

            # Create trade record
            # Calculate entry date (typically next day after signal to avoid look-ahead bias)
            entry_date = date + timedelta(days=1)

            trade = {
                'signal_date': date,
                'entry_date': entry_date,
                'exit_date': exit_date,
                'signal_direction': signal_direction,
                'confidence_score': confidence_score,
                'spx_price': spx_price,
                'short_strike': short_option['Strike'],
                'long_strike': long_option['Strike'],
                'short_expiry': short_option['expiry_date'],
                'long_expiry': long_option['expiry_date'],
                'short_entry_price': short_premium,
                'long_entry_price': long_premium,
                'net_credit': net_credit,
                'short_exit_price': exit_short_price,
                'long_exit_price': exit_long_price,
                'contracts': base_contracts,
                'short_pnl': short_pnl,
                'long_pnl': long_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0,
                'spread_width': long_option['Strike'] - short_option['Strike'],
                'max_profit': net_credit * base_contracts * 100,
                'max_loss': (long_option['Strike'] - short_option['Strike'] - net_credit) * base_contracts * 100
            }

            trades.append(trade)
            successful_trades += 1

        print(f"✅ Executed {successful_trades} call spread trades")
        print(f"❌ Failed to execute {failed_trades} trades")

        if not trades:
            print("❌ No call spread trades executed")
            return None

        # Convert to DataFrame and calculate performance
        trades_df = pd.DataFrame(trades)

        # Calculate performance metrics
        total_pnl = trades_df['net_pnl'].sum()
        win_rate = trades_df['win_loss_flag'].mean() * 100
        total_trades = len(trades_df)

        # Calculate returns
        starting_capital = 100000  # $100K
        total_return = (total_pnl / starting_capital) * 100

        # Calculate max drawdown
        trades_df_sorted = trades_df.sort_values('exit_date')
        trades_df_sorted['cumulative_pnl'] = trades_df_sorted['net_pnl'].cumsum()
        trades_df_sorted['running_max'] = trades_df_sorted['cumulative_pnl'].expanding().max()
        trades_df_sorted['drawdown'] = trades_df_sorted['cumulative_pnl'] - trades_df_sorted['running_max']
        max_drawdown = abs(trades_df_sorted['drawdown'].min() / starting_capital * 100)

        # Calculate profit factor
        winning_trades = trades_df[trades_df['net_pnl'] > 0]
        losing_trades = trades_df[trades_df['net_pnl'] < 0]

        if len(losing_trades) > 0:
            profit_factor = winning_trades['net_pnl'].sum() / abs(losing_trades['net_pnl'].sum())
        else:
            profit_factor = float('inf')

        # Save trades
        os.makedirs('trades', exist_ok=True)
        trades_df.to_csv('trades/call_spread_trades.csv', index=False)

        # Print results
        print("\n" + "=" * 60)
        print("📊 CALL SPREAD STRATEGY RESULTS")
        print("=" * 60)
        print(f"💰 Total Return: {total_return:.1f}%")
        print(f"🎯 Win Rate: {win_rate:.1f}%")
        print(f"📈 Total P&L: ${total_pnl:,.0f}")
        print(f"💵 Final Capital: ${starting_capital + total_pnl:,.0f}")
        print(f"📉 Max Drawdown: {max_drawdown:.1f}%")
        print(f"⚖️ Profit Factor: {profit_factor:.2f}")
        print(f"📊 Total Trades: {total_trades}")

        if len(winning_trades) > 0:
            print(f"💪 Avg Win: ${winning_trades['net_pnl'].mean():,.0f}")
        if len(losing_trades) > 0:
            print(f"💔 Avg Loss: ${losing_trades['net_pnl'].mean():,.0f}")

        print(f"📊 Avg Spread Width: {trades_df['spread_width'].mean():.0f} points")
        print(f"💰 Avg Net Credit: ${trades_df['net_credit'].mean():.2f}")
        print(f"📊 Avg Contracts: {trades_df['contracts'].mean():.1f}")

        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'total_pnl': total_pnl,
            'trades_df': trades_df
        }

    def revalue_spread_position(self, exit_date, short_position, long_position):
        """
        Revalue a call spread position by finding both legs on the exit date

        Args:
            exit_date (datetime): Date to revalue the position
            short_position (dict): Short leg details (strike, expiry, entry_price)
            long_position (dict): Long leg details (strike, expiry, entry_price)

        Returns:
            tuple: (short_exit_price, long_exit_price) or (None, None) if failed
        """
        if self.spx_options_data is None:
            print(f"⚠️ No options data available for spread revaluation")
            return None, None

        # Revalue short leg
        short_exit_price = self._revalue_single_leg(exit_date, short_position)

        # Revalue long leg
        long_exit_price = self._revalue_single_leg(exit_date, long_position)

        if short_exit_price is None or long_exit_price is None:
            print(f"⚠️ Could not revalue spread position on {exit_date.date()}")
            return None, None

        return short_exit_price, long_exit_price

    def _revalue_single_leg(self, exit_date, position):
        """
        Revalue a single option leg

        Args:
            exit_date (datetime): Exit date
            position (dict): Position details (strike, expiry, entry_price)

        Returns:
            float: Exit price or None if failed
        """
        strike = position['strike']
        expiry = position['expiry']
        entry_price = position['entry_price']

        # Check if option has expired
        if exit_date >= expiry:
            print(f"   📅 Option expired: Strike {strike}, Expiry {expiry.date()}")
            return 0.05  # Expired options worth minimal value

        # Find the EXACT same option (same strike AND same expiry) on the exit date
        exact_option = self.spx_options_data[
            (self.spx_options_data['date'] == exit_date) &
            (self.spx_options_data['Strike'] == strike) &
            (self.spx_options_data['expiry_date'] == expiry) &
            (self.spx_options_data['Call/Put'] == 'c')
        ]

        if len(exact_option) > 0:
            option = exact_option.iloc[0]
            exit_price = float(option.get('Last Trade Price', 0))
            if exit_price > 0:
                dte = (expiry - exit_date).days
                print(f"   ✅ Found exact option: Strike {strike}, Price ${exit_price:.2f} ({dte} DTE)")
                return exit_price

        # If exact option not found, try nearby dates (±3 days)
        for day_offset in [-3, -2, -1, 1, 2, 3]:
            nearby_date = exit_date + timedelta(days=day_offset)
            if nearby_date.weekday() >= 5:  # Skip weekends
                continue

            nearby_option = self.spx_options_data[
                (self.spx_options_data['date'] == nearby_date) &
                (self.spx_options_data['Strike'] == strike) &
                (self.spx_options_data['expiry_date'] == expiry) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            if len(nearby_option) > 0:
                option = nearby_option.iloc[0]
                exit_price = float(option.get('Last Trade Price', 0))
                if exit_price > 0:
                    dte = (expiry - nearby_date).days
                    print(f"   ⚠️ Found nearby option: Strike {strike}, Price ${exit_price:.2f} ({dte} DTE, {day_offset:+d} days)")
                    return exit_price

        print(f"   ❌ No valid option found for Strike {strike} around {exit_date.date()}")
        return None

    def calculate_vix_rsi(self, vix_data, period=2):
        """Calculate VIX RSI for technical analysis"""
        vix_values = vix_data['vix'].values

        # Calculate price changes
        deltas = np.diff(vix_values)

        # Separate gains and losses
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        # Calculate RSI
        rsi_values = np.full(len(vix_values), np.nan)

        if len(gains) >= period:
            # Calculate initial averages
            avg_gain = np.mean(gains[:period])
            avg_loss = np.mean(losses[:period])

            for i in range(period, len(gains)):
                # Smoothed averages
                avg_gain = (avg_gain * (period - 1) + gains[i]) / period
                avg_loss = (avg_loss * (period - 1) + losses[i]) / period

                if avg_loss != 0:
                    rs = avg_gain / avg_loss
                    rsi_values[i + 1] = 100 - (100 / (1 + rs))
                else:
                    rsi_values[i + 1] = 100

        return rsi_values

    def calculate_vrp_technical_indicators(self, market_data):
        """Calculate VRP technical indicators (RSI, Moving Averages)"""

        # VRP RSI (14-day)
        vrp_values = market_data['vrp_avg'].fillna(0).values
        vrp_deltas = np.diff(vrp_values)

        vrp_gains = np.where(vrp_deltas > 0, vrp_deltas, 0)
        vrp_losses = np.where(vrp_deltas < 0, -vrp_deltas, 0)

        vrp_rsi = np.full(len(vrp_values), 50)  # Default neutral RSI

        period = 14
        if len(vrp_gains) >= period:
            avg_gain = np.mean(vrp_gains[:period])
            avg_loss = np.mean(vrp_losses[:period])

            for i in range(period, len(vrp_gains)):
                avg_gain = (avg_gain * (period - 1) + vrp_gains[i]) / period
                avg_loss = (avg_loss * (period - 1) + vrp_losses[i]) / period

                if avg_loss != 0:
                    rs = avg_gain / avg_loss
                    vrp_rsi[i + 1] = 100 - (100 / (1 + rs))
                else:
                    vrp_rsi[i + 1] = 100

        # VRP Moving Averages
        vrp_ma_5 = pd.Series(vrp_values).rolling(window=5, min_periods=1).mean().values
        vrp_ma_10 = pd.Series(vrp_values).rolling(window=10, min_periods=1).mean().values
        vrp_ma_20 = pd.Series(vrp_values).rolling(window=20, min_periods=1).mean().values

        return vrp_rsi, vrp_ma_5, vrp_ma_10, vrp_ma_20

    def generate_enhanced_signals(self, market_data):
        """Generate tightened, high-quality trading signals based on VIX and VRP technical analysis"""

        signals = []

        # Check available columns
        print(f"📊 Market data columns: {list(market_data.columns)}")
        print(f"📊 Processing {len(market_data)} trading days for RELAXED signals...")

        # Calculate VIX RSI (2-day for short-term signals)
        vix_rsi_2d = self.calculate_vix_rsi(market_data, period=2)

        # Calculate VRP technical indicators
        vrp_rsi, vrp_ma_5, vrp_ma_10, vrp_ma_20 = self.calculate_vrp_technical_indicators(market_data)

        # Add technical indicators to market data
        market_data = market_data.copy()
        market_data['vix_rsi_2d'] = vix_rsi_2d
        market_data['vrp_rsi'] = vrp_rsi
        market_data['vrp_ma_5'] = vrp_ma_5
        market_data['vrp_ma_10'] = vrp_ma_10
        market_data['vrp_ma_20'] = vrp_ma_20

        signal_count = 0

        # Process each trading day
        for i, (date, row) in enumerate(market_data.iterrows()):
            # Convert to pandas Timestamp if it's not already
            if not isinstance(date, pd.Timestamp):
                date = pd.to_datetime(date)

            vix = row['vix']
            vrp_avg = row.get('vrp_avg', np.nan)
            vix_rsi = row.get('vix_rsi_2d', 50)
            vrp_rsi_val = row.get('vrp_rsi', 50)
            vix_momentum = row.get('vix_momentum', 0)

            # Skip if no VRP data
            if np.isnan(vrp_avg):
                continue

            # RELAXED VIX REGIME ANALYSIS - More permissive
            vix_regime = self.classify_vix_regime(vix)
            if vix_regime in ['VERY_HIGH']:  # Only skip extreme volatility
                continue

            # Trade in broader VIX environments
            if vix > 30:  # Relaxed VIX threshold
                continue

            # CORE SIGNAL GENERATION - RELAXED CRITERIA
            signal_direction = None
            confidence_score = 0.0
            conditions = []

            # 1. VRP PRIMARY SIGNAL - RELAXED (More inclusive)
            vrp_signal_strength = 0
            if vrp_avg < -5:  # Strong negative VRP
                vrp_signal_strength = 0.8
                conditions.append("Strong Negative VRP")
            elif vrp_avg < -3:  # Moderate negative VRP
                vrp_signal_strength = 0.6
                conditions.append("Moderate Negative VRP")
            elif vrp_avg < -1:  # Mild negative VRP
                vrp_signal_strength = 0.4
                conditions.append("Mild Negative VRP")
            else:
                continue  # Still need some negative VRP

            # 2. VIX TECHNICAL ANALYSIS - RELAXED
            vix_technical_boost = 0
            vix_technical_signal = False
            if not np.isnan(vix_rsi):
                if vix_rsi < 25:  # Relaxed oversold VIX
                    vix_technical_boost = 0.25
                    vix_technical_signal = True
                    conditions.append("VIX Oversold")
                elif vix_rsi > 75:  # Relaxed overbought VIX
                    vix_technical_boost = 0.2
                    vix_technical_signal = True
                    conditions.append("VIX Overbought")

            # 3. VRP TECHNICAL CONFIRMATION - OPTIONAL
            vrp_technical_boost = 0
            vrp_technical_signal = False
            if not np.isnan(vrp_rsi_val):
                if vrp_rsi_val < 35:  # Relaxed VRP oversold
                    vrp_technical_boost = 0.15
                    vrp_technical_signal = True
                    conditions.append("VRP RSI Oversold")
                elif vrp_rsi_val > 65:  # VRP overbought
                    vrp_technical_boost = 0.1
                    vrp_technical_signal = True
                    conditions.append("VRP RSI Overbought")

            # 4. VIX MOMENTUM ANALYSIS - RELAXED
            momentum_boost = 0
            momentum_signal = False
            if vix_momentum < -2:  # Strong VIX decline
                momentum_boost = 0.25
                momentum_signal = True
                conditions.append("Strong VIX Decline")
            elif vix_momentum < -1:  # Moderate VIX decline
                momentum_boost = 0.15
                momentum_signal = True
                conditions.append("Moderate VIX Decline")
            elif vix_momentum < -0.5:  # Mild VIX decline
                momentum_boost = 0.1
                momentum_signal = True
                conditions.append("Mild VIX Decline")

            # 5. VRP MOVING AVERAGE SIGNALS - RELAXED
            ma_boost = 0
            ma_signal = False
            if not np.isnan(row.get('vrp_ma_5', np.nan)) and not np.isnan(row.get('vrp_ma_10', np.nan)):
                if row['vrp_ma_5'] < row['vrp_ma_10']:  # Simple bearish VRP trend
                    ma_boost = 0.1
                    ma_signal = True
                    conditions.append("VRP MA Bearish")

            # RELAXED CONFIRMATION REQUIREMENTS
            confirmations_count = sum([
                vix_technical_signal,
                vrp_technical_signal,
                momentum_signal,
                ma_signal
            ])

            # Require at least 1 confirmation beyond VRP (much more relaxed)
            if confirmations_count < 1:
                continue

            # COMBINE SIGNALS
            base_confidence = vrp_signal_strength
            total_boost = vix_technical_boost + vrp_technical_boost + momentum_boost + ma_boost

            final_confidence = min(0.95, base_confidence + total_boost)

            # REVERSED SIGNAL THRESHOLD - Analysis showed BULLISH signals lose money
            # Change to BEARISH signals or use PUT spreads instead of CALL spreads
            if final_confidence >= 0.5:  # Much lower confidence threshold
                signal_direction = 'BEARISH'  # REVERSED: Was BULLISH, now BEARISH
                confidence_score = final_confidence

                signal_count += 1
                signals.append({
                    'date': date,
                    'signal_direction': signal_direction,
                    'condition': " + ".join(conditions),
                    'confidence_score': confidence_score,
                    'vix': vix,
                    'vrp_avg': vrp_avg,
                    'vix_regime': vix_regime,
                    'vix_rsi_2d': vix_rsi,
                    'vrp_rsi': vrp_rsi_val,
                    'confirmations': confirmations_count
                })

        print(f"✅ Generated {signal_count} RELAXED signals from {len(market_data)} trading days ({signal_count/len(market_data)*100:.1f}% signal rate)")

        if signal_count > 0:
            avg_confidence = np.mean([s['confidence_score'] for s in signals])
            avg_confirmations = np.mean([s['confirmations'] for s in signals])
            print(f"📊 Average confidence score: {avg_confidence:.2f}")
            print(f"📊 Average confirmations: {avg_confirmations:.1f}")

            # Show signal distribution
            conditions = [s['condition'] for s in signals]
            from collections import Counter
            condition_counts = Counter(conditions)
            print("📊 RELAXED Signal conditions:")
            for condition, count in condition_counts.most_common()[:5]:
                print(f"   {condition}: {count} signals")
        else:
            print("⚠️ No signals met the relaxed criteria")

        return signals

    def classify_vix_regime(self, vix):
        """Classify VIX regime based on level"""
        if vix < 15:
            return 'LOW'
        elif vix < 22:
            return 'NORMAL'
        elif vix < 30:
            return 'HIGH'
        else:
            return 'VERY_HIGH'

if __name__ == "__main__":
    print("🎯 CALL SPREAD STRATEGY EXECUTION")
    print("=" * 50)

    # Test both configurations
    print("\n" + "="*60)
    print("🔄 TESTING BOTH CONFIGURATIONS")
    print("="*60)

    # Test 1: Reversed Debit Spread (buy closer, sell further) - Testing first
    print("\n📊 TEST 1: REVERSED DEBIT SPREAD")
    print("-" * 40)
    strategy_debit = CallSpreadStrategy(reverse_legs=True)
    results_debit = strategy_debit.execute_call_spread_strategy(holding_days=3)

    # Test 2: Standard Credit Spread (sell closer, buy further)
    print("\n📊 TEST 2: STANDARD CREDIT SPREAD")
    print("-" * 40)
    strategy_credit = CallSpreadStrategy(reverse_legs=False)
    results_credit = strategy_credit.execute_call_spread_strategy(holding_days=3)

    # Compare results
    print("\n" + "="*60)
    print("📊 COMPARISON SUMMARY")
    print("="*60)

    if results_debit and 'total_trades' in results_debit:
        print(f"🔄 DEBIT SPREAD: {results_debit['total_trades']} trades, PnL: ${results_debit['total_pnl']:.2f}")
    else:
        print("🔄 DEBIT SPREAD: No valid results")

    if results_credit and 'total_trades' in results_credit:
        print(f"💰 CREDIT SPREAD: {results_credit['total_trades']} trades, PnL: ${results_credit['total_pnl']:.2f}")
    else:
        print("💰 CREDIT SPREAD: No valid results")

    print("\n✅ Strategy comparison completed!")
