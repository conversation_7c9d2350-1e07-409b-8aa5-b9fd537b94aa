#!/usr/bin/env python3
"""
VRP-Enhanced Overnight Call Spread Strategy
Uses Volatility Risk Premium to identify when calls are cheap
Buy cheap calls and finance them when VRP is favorable
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta

class VRPEnhancedOvernightStrategy:
    """
    VRP-enhanced overnight call spread strategy
    - Use VRP to identify when calls are cheap
    - Buy call when VRP is negative (options underpriced)
    - Finance with further OTM call
    - Hold overnight to capture S&P gains
    """
    
    def __init__(self):
        self.spx_options_data = None
        self.market_data = None
        self.spread_width = 150  # Points between strikes
        self.otm_offset = 25     # Points OTM for long call
        
    def load_spx_options_data(self):
        """Load SPX options data from CSV files"""
        print("📊 Loading SPX options data...")
        
        options_dir = "/Users/<USER>/Downloads/optionhistory"
        if not os.path.exists(options_dir):
            print(f"❌ Options directory not found: {options_dir}")
            return None

        # Find all SPX options files
        all_files = []
        for year in range(2023, 2026):
            for quarter in range(1, 5):
                pattern = f"{options_dir}/{year}_q{quarter}_option_chain/spx_complete_{year}_q{quarter}.csv"
                files = glob.glob(pattern)
                all_files.extend(files)

        if not all_files:
            print("❌ No SPX options files found")
            return None

        print(f"📊 Found {len(all_files)} SPX options files")

        # Load and combine all files
        all_data = []
        for file in all_files:
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"   ✅ Loaded {len(df)} records from {os.path.basename(file)}")
            except Exception as e:
                print(f"   ❌ Error loading {file}: {e}")

        if not all_data:
            return None

        # Combine all data
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # Convert date columns
        combined_data['date'] = pd.to_datetime(combined_data['date'])
        combined_data['expiry_date'] = pd.to_datetime(combined_data['Expiry Date'])
        
        # Use Last Trade Price as the option price
        combined_data['option_price'] = combined_data['Last Trade Price']
        
        # Filter for valid data
        combined_data = combined_data.dropna(subset=['option_price', 'Strike', 'spx_close'])
        combined_data = combined_data[combined_data['option_price'] > 0]
        
        print(f"✅ Combined SPX options data: {len(combined_data)} records")
        print(f"   Date range: {combined_data['date'].min()} to {combined_data['date'].max()}")
        print(f"   SPX range: {combined_data['spx_close'].min():.0f} to {combined_data['spx_close'].max():.0f}")
        
        return combined_data

    def calculate_vrp(self):
        """
        Calculate Volatility Risk Premium (VRP)
        VRP = Implied Volatility - Realized Volatility
        Negative VRP = options are cheap (good time to buy)
        """
        print("📊 Calculating VRP (Volatility Risk Premium)...")
        
        # Get unique dates and SPX prices
        daily_data = self.spx_options_data.groupby('date').agg({
            'spx_close': 'first'
        }).reset_index()
        
        daily_data = daily_data.sort_values('date')
        
        # Calculate 20-day realized volatility
        daily_data['spx_returns'] = daily_data['spx_close'].pct_change()
        daily_data['realized_vol_20d'] = daily_data['spx_returns'].rolling(20).std() * np.sqrt(252) * 100
        
        # Calculate implied volatility from ATM options
        vrp_data = []
        
        for date in daily_data['date']:
            spx_price = daily_data[daily_data['date'] == date]['spx_close'].iloc[0]
            realized_vol = daily_data[daily_data['date'] == date]['realized_vol_20d'].iloc[0]
            
            if pd.isna(realized_vol):
                continue
                
            # Get ATM options for this date (both calls and puts)
            day_options = self.spx_options_data[
                (self.spx_options_data['date'] == date) &
                (self.spx_options_data['option_price'] > 0)
            ].copy()
            
            if len(day_options) == 0:
                continue
                
            # Calculate days to expiry
            day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days
            
            # Filter for 20-40 days to expiry (front month)
            day_options = day_options[
                (day_options['days_to_expiry'] >= 20) & 
                (day_options['days_to_expiry'] <= 40)
            ]
            
            if len(day_options) == 0:
                continue
                
            # Find ATM options (closest to SPX price)
            day_options['strike_distance'] = abs(day_options['Strike'] - spx_price)
            atm_options = day_options[day_options['strike_distance'] <= 50]  # Within 50 points of ATM
            
            if len(atm_options) == 0:
                continue
                
            # Calculate simple implied volatility proxy
            # For ATM options: IV ≈ (Option Price / SPX Price) * sqrt(365/DTE) * 100
            atm_options = atm_options.copy()
            atm_options['iv_proxy'] = (atm_options['option_price'] / spx_price) * np.sqrt(365 / atm_options['days_to_expiry']) * 100
            
            # Use median IV to reduce noise
            implied_vol = atm_options['iv_proxy'].median()
            
            if pd.isna(implied_vol) or implied_vol <= 0:
                continue
                
            # Calculate VRP
            vrp = implied_vol - realized_vol
            
            vrp_data.append({
                'date': date,
                'spx_close': spx_price,
                'implied_vol': implied_vol,
                'realized_vol': realized_vol,
                'vrp': vrp
            })
        
        vrp_df = pd.DataFrame(vrp_data)
        
        if len(vrp_df) > 0:
            print(f"✅ Calculated VRP for {len(vrp_df)} dates")
            print(f"   Average VRP: {vrp_df['vrp'].mean():.2f}")
            print(f"   VRP range: {vrp_df['vrp'].min():.2f} to {vrp_df['vrp'].max():.2f}")
            print(f"   Negative VRP days: {(vrp_df['vrp'] < 0).sum()}/{len(vrp_df)} ({(vrp_df['vrp'] < 0).mean()*100:.1f}%)")
        else:
            print("❌ No VRP data calculated")
            
        return vrp_df

    def find_call_spread_options(self, date, spx_price):
        """Find call spread options for overnight strategy"""
        
        # Get call options for this date
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == date) &
            (self.spx_options_data['Call/Put'] == 'c')
        ].copy()

        if len(day_options) == 0:
            return None, None

        # Calculate days to expiry
        day_options['expiry_date'] = pd.to_datetime(day_options['expiry_date'])
        day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days

        # Filter for options with 20+ days to expiry
        day_options = day_options[day_options['days_to_expiry'] >= 20].copy()
        if len(day_options) == 0:
            return None, None

        # Round SPX to nearest 25
        quotient = spx_price / 25
        remainder = quotient - int(quotient)
        spx_rounded = (int(quotient) + 1) * 25 if remainder > 0.5 else int(quotient) * 25

        # STRIKE SELECTION
        # Buy call 25 points OTM (the "cheap" call we want to buy)
        long_strike = spx_rounded + self.otm_offset
        # Sell call 150 points further out (financing call)
        short_strike = long_strike + self.spread_width

        # Find closest available strikes
        long_option = day_options.iloc[(day_options['Strike'] - long_strike).abs().argsort()[:1]]
        short_option = day_options.iloc[(day_options['Strike'] - short_strike).abs().argsort()[:1]]

        if len(long_option) == 0 or len(short_option) == 0:
            return None, None

        # Ensure we have valid options with prices
        long_option = long_option.iloc[0]
        short_option = short_option.iloc[0]

        if long_option['option_price'] <= 0 or short_option['option_price'] <= 0:
            return None, None

        return long_option, short_option

    def get_next_trading_day(self, date):
        """Get the next trading day (simple weekend logic)"""
        next_day = date + timedelta(days=1)
        while next_day.weekday() >= 5:  # Skip weekends
            next_day += timedelta(days=1)
        return next_day

    def execute_vrp_enhanced_strategy(self):
        """Execute the VRP-enhanced overnight call spread strategy"""
        print("🚀 EXECUTING VRP-ENHANCED OVERNIGHT CALL SPREAD STRATEGY")
        print("=" * 70)
        print("📋 Strategy: Buy cheap calls when VRP is negative")
        print("📋 Finance with further OTM calls, hold overnight")
        print("=" * 70)

        # Load options data
        self.spx_options_data = self.load_spx_options_data()
        if self.spx_options_data is None:
            print("❌ Strategy execution failed - no options data available")
            return None

        # Calculate VRP
        self.market_data = self.calculate_vrp()
        if self.market_data is None or len(self.market_data) == 0:
            print("❌ Strategy execution failed - no VRP data available")
            return None

        trades = []
        successful_trades = 0
        failed_trades = 0

        # Filter for dates with VRP data
        test_dates = self.market_data['date'].tolist()
        print(f"📊 Testing on {len(test_dates)} dates with VRP data")

        for date in test_dates:
            # Get VRP for this date
            vrp_data = self.market_data[self.market_data['date'] == date]
            if len(vrp_data) == 0:
                failed_trades += 1
                continue
                
            vrp = vrp_data['vrp'].iloc[0]
            implied_vol = vrp_data['implied_vol'].iloc[0]
            realized_vol = vrp_data['realized_vol'].iloc[0]
            spx_price = vrp_data['spx_close'].iloc[0]

            # VRP FILTER: Only trade when VRP is negative (options are cheap)
            if vrp >= 0:
                failed_trades += 1
                continue

            # Additional filter: Skip if implied vol is too high (> 30%)
            if implied_vol > 30:
                failed_trades += 1
                continue

            # Find call spread options
            long_option, short_option = self.find_call_spread_options(date, spx_price)
            if long_option is None or short_option is None:
                failed_trades += 1
                continue

            # Calculate spread parameters
            long_premium = long_option['option_price']   # We pay this (buy cheap call)
            short_premium = short_option['option_price'] # We receive this (financing call)
            net_debit = long_premium - short_premium     # Net debit paid

            # Skip if debit is too large or if we can't get decent financing
            if net_debit <= 2.0 or net_debit >= 40.0:
                failed_trades += 1
                continue

            # VRP-BASED POSITION SIZING
            # More negative VRP = larger position (options are cheaper)
            if vrp < -5:
                contracts = 8  # Large position when options are very cheap
            elif vrp < -2:
                contracts = 6  # Medium position when options are cheap
            else:
                contracts = 4  # Small position when options are slightly cheap

            # Calculate exit date (next trading day)
            exit_date = self.get_next_trading_day(date)

            # Find exit prices
            exit_long_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == long_option['Strike']) &
                (self.spx_options_data['expiry_date'] == long_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            exit_short_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == short_option['Strike']) &
                (self.spx_options_data['expiry_date'] == short_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            if len(exit_long_options) == 0 or len(exit_short_options) == 0:
                failed_trades += 1
                continue

            exit_long_price = exit_long_options['option_price'].iloc[0]
            exit_short_price = exit_short_options['option_price'].iloc[0]

            # Calculate P&L
            long_pnl = (exit_long_price - long_premium) * contracts * 100   # Profit if call goes up
            short_pnl = (short_premium - exit_short_price) * contracts * 100 # Profit if financing call stays OTM
            total_pnl = long_pnl + short_pnl

            # Commission
            commission = contracts * 2 * 2  # $2 per contract per leg
            net_pnl = total_pnl - commission

            # Get exit SPX price
            exit_spx_price = exit_long_options['spx_close'].iloc[0]
            spx_change = exit_spx_price - spx_price

            # Create trade record
            trade = {
                'date': date,
                'exit_date': exit_date,
                'spx_entry': spx_price,
                'spx_exit': exit_spx_price,
                'spx_change': spx_change,
                'vrp': vrp,
                'implied_vol': implied_vol,
                'realized_vol': realized_vol,
                'long_strike': long_option['Strike'],
                'short_strike': short_option['Strike'],
                'long_entry_price': long_premium,
                'short_entry_price': short_premium,
                'net_debit': net_debit,
                'long_exit_price': exit_long_price,
                'short_exit_price': exit_short_price,
                'contracts': contracts,
                'long_pnl': long_pnl,
                'short_pnl': short_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0
            }

            trades.append(trade)
            successful_trades += 1

            # Show first few trades for debugging
            if successful_trades <= 5:
                print(f"✅ Trade {successful_trades}: VRP {vrp:.2f}, SPX {spx_price:.0f}→{exit_spx_price:.0f} ({spx_change:+.0f}), "
                      f"{contracts} contracts, Debit ${net_debit:.2f}, P&L ${net_pnl:+,.0f}")

        # Create results DataFrame
        if trades:
            trades_df = pd.DataFrame(trades)
            
            # Ensure trades directory exists
            os.makedirs('trades', exist_ok=True)
            
            # Save trades
            trades_df.to_csv('trades/vrp_enhanced_overnight_trades.csv', index=False)
            
            # Calculate performance metrics
            total_pnl = trades_df['net_pnl'].sum()
            win_rate = trades_df['win_loss_flag'].mean() * 100
            total_return = (total_pnl / (100000)) * 100  # Assuming $100k starting capital
            
            winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
            losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
            
            avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
            avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
            
            print(f"\n🎯 VRP-ENHANCED STRATEGY PERFORMANCE SUMMARY")
            print("=" * 60)
            print(f"📊 Total Trades: {len(trades_df)}")
            print(f"✅ Successful Setups: {successful_trades}")
            print(f"❌ Failed Setups: {failed_trades}")
            print(f"🎯 Win Rate: {win_rate:.1f}%")
            print(f"💰 Total P&L: ${total_pnl:+,.0f}")
            print(f"📈 Total Return: {total_return:+.1f}%")
            print(f"💪 Average Win: ${avg_win:+,.0f}")
            print(f"💔 Average Loss: ${avg_loss:+,.0f}")
            print(f"📦 Average Position Size: {trades_df['contracts'].mean():.1f} contracts")
            print(f"📊 Average VRP: {trades_df['vrp'].mean():.2f}")
            print(f"📊 Average SPX Change: {trades_df['spx_change'].mean():+.1f} points")
            print(f"📊 SPX Up Days: {(trades_df['spx_change'] > 0).sum()}/{len(trades_df)} ({(trades_df['spx_change'] > 0).mean()*100:.1f}%)")
            
            return trades_df
        else:
            print("❌ No successful trades executed")
            return None


if __name__ == "__main__":
    print("🎯 VRP-ENHANCED OVERNIGHT CALL SPREAD STRATEGY")
    print("=" * 60)
    
    # Execute strategy
    strategy = VRPEnhancedOvernightStrategy()
    results = strategy.execute_vrp_enhanced_strategy()
    
    if results is not None:
        print(f"\n✅ VRP-enhanced strategy execution completed!")
        print(f"📊 Check trades/vrp_enhanced_overnight_trades.csv for detailed results")
    else:
        print("❌ VRP-enhanced strategy execution failed")
