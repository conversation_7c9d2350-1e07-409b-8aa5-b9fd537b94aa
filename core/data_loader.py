"""
Unified Data Loader for JPM Options Trading Strategies
Loads VIX data, SPX options data, and performs VRP calculations
Preserves exact logic from working strategies
"""
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

from config.constants import *
from utils.logger import OptionsLogger

class DataLoader:
    """Unified data loading for all trading strategies"""
    
    def __init__(self, logger: Optional[OptionsLogger] = None):
        self.logger = logger
        self.start_date = pd.to_datetime(START_DATE)
        self.end_date = pd.to_datetime(END_DATE)
        self.spx_options_data = None
        self.market_data = None
        
        # VIX data file paths (from original strategy)
        self.vix_data_files = {
            'VIX': VIX_DATA_PATH,
            'VIX9D': VIX9D_DATA_PATH
        }
    
    def load_real_spx_options_data(self) -> Optional[pd.DataFrame]:
        """
        Load real SPX options data with proper date handling
        Preserves exact logic from final_strategy_clean.py
        """
        if self.logger:
            self.logger.logger.info("Loading real SPX options data...")
        else:
            print("📊 Loading real SPX options data...")
        
        if not os.path.exists(SPX_OPTIONS_BASE_PATH):
            error_msg = f"Options directory not found: {SPX_OPTIONS_BASE_PATH}"
            if self.logger:
                self.logger.logger.error(error_msg)
            else:
                print(f"❌ {error_msg}")
            return None
        
        # Find all options files
        options_files = []
        for root, _, files in os.walk(SPX_OPTIONS_BASE_PATH):
            for file in files:
                if 'spx_complete' in file.lower() and file.endswith('.csv'):
                    file_path = os.path.join(root, file)
                    options_files.append(file_path)
        
        if not options_files:
            error_msg = "No SPX options files found"
            if self.logger:
                self.logger.logger.error(error_msg)
            else:
                print(f"❌ {error_msg}")
            return None
        
        info_msg = f"Found {len(options_files)} SPX options files"
        if self.logger:
            self.logger.logger.info(info_msg)
        else:
            print(f"📁 {info_msg}")
        
        # Load and combine options data
        combined_options = []
        files_loaded = 0
        
        for file_path in sorted(options_files):
            try:
                file_msg = f"Loading: {os.path.basename(file_path)}"
                if self.logger:
                    self.logger.logger.debug(file_msg)
                else:
                    print(f"   {file_msg}")
                    
                df = pd.read_csv(file_path)
                
                # Parse dates properly
                df['date'] = pd.to_datetime(df['date'])
                df['expiry_date'] = pd.to_datetime(df['Expiry Date'])
                
                # Filter for our date range
                df = df[
                    (df['date'] >= self.start_date) & 
                    (df['date'] <= self.end_date)
                ].copy()
                
                if len(df) > 0:
                    # Calculate days to expiry
                    df['days_to_expiry'] = (df['expiry_date'] - df['date']).dt.days
                    
                    # Filter for relevant options (using exact original criteria)
                    df = df[
                        (df['Last Trade Price'] > 0.50) &  # OPTIONS_MIN_PRICE
                        (df['Last Trade Price'] < 100.0) &  # OPTIONS_MAX_PRICE
                        (df['days_to_expiry'] >= 25) &  # OPTIONS_MIN_EXPIRY_DAYS
                        (df['days_to_expiry'] <= 35) &  # OPTIONS_MAX_EXPIRY_DAYS
                        (df['Volume'] > 0)  # OPTIONS_MIN_VOLUME
                    ].copy()
                    
                    if len(df) > 0:
                        # Keep essential columns
                        essential_columns = [
                            'date', 'expiry_date', 'Strike', 'Call/Put', 
                            'Last Trade Price', 'Bid Price', 'Ask Price', 
                            'Volume', 'days_to_expiry', 'spx_close'
                        ]
                        
                        # Only keep columns that exist
                        available_columns = [col for col in essential_columns if col in df.columns]
                        df = df[available_columns].copy()
                        
                        # Rename for consistency with call spread strategy
                        df = df.rename(columns={
                            'expiry_date': 'expiry',
                            'Last Trade Price': 'price',
                            'Bid Price': 'bid',
                            'Ask Price': 'ask'
                        })
                        
                        combined_options.append(df)
                        files_loaded += 1
                        
            except Exception as e:
                error_msg = f"Error loading {file_path}: {e}"
                if self.logger:
                    self.logger.logger.warning(error_msg)
                else:
                    print(f"   ⚠️ {error_msg}")
                continue
        
        if combined_options:
            options_df = pd.concat(combined_options, ignore_index=True)
            options_df = options_df.sort_values('date')
            
            success_msg = f"Loaded real SPX options data: {len(options_df):,} records from {files_loaded} files"
            date_range_msg = f"Date range: {options_df['date'].min()} to {options_df['date'].max()}"
            price_range_msg = f"Price range: ${options_df['price'].min():.2f} - ${options_df['price'].max():.2f}"
            spx_range_msg = f"SPX range: {options_df['spx_close'].min():.0f} - {options_df['spx_close'].max():.0f}"
            
            if self.logger:
                self.logger.logger.info(success_msg)
                self.logger.logger.info(date_range_msg)
                self.logger.logger.info(price_range_msg)
                self.logger.logger.info(spx_range_msg)
            else:
                print(f"✅ {success_msg}")
                print(f"   {date_range_msg}")
                print(f"   {price_range_msg}")
                print(f"   {spx_range_msg}")
            
            self.spx_options_data = options_df
            return options_df
        else:
            error_msg = "No valid SPX options data loaded"
            if self.logger:
                self.logger.logger.error(error_msg)
            else:
                print(f"❌ {error_msg}")
            return None
    
    def calculate_real_vrp_from_options(self, vix_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate VRP using real SPX options data with NO forward-looking bias
        Preserves exact logic from final_strategy_clean.py
        """
        if self.logger:
            self.logger.logger.info("Calculating VRP from real SPX options data (no forward-looking bias)...")
        else:
            print("🔍 Calculating VRP from real SPX options data (no forward-looking bias)...")
        
        if self.spx_options_data is None:
            error_msg = "No SPX options data available for VRP calculation"
            if self.logger:
                self.logger.logger.error(error_msg)
            else:
                print(f"❌ {error_msg}")
            return vix_data
        
        vrp_results = []
        
        for date, row in vix_data.iterrows():
            current_vix = row['vix']
            
            # Get SPX options data UP TO (but not including) current date
            # This ensures no forward-looking bias
            historical_options = self.spx_options_data[
                self.spx_options_data['date'] < date  # STRICTLY LESS THAN current date
            ].copy()
            
            if len(historical_options) == 0:
                # No historical data available yet
                vrp_results.append({
                    'date': date,
                    'vrp_10d': np.nan,
                    'vrp_20d': np.nan,
                    'vrp_30d': np.nan,
                    'vrp_avg': np.nan
                })
                continue
            
            # Calculate realized volatility from SPX price changes
            # Get recent SPX closes for volatility calculation
            recent_spx = historical_options.groupby('date')['spx_close'].first().tail(30)
            
            if len(recent_spx) < 10:
                vrp_results.append({
                    'date': date,
                    'vrp_10d': np.nan,
                    'vrp_20d': np.nan,
                    'vrp_30d': np.nan,
                    'vrp_avg': np.nan
                })
                continue
            
            # Calculate realized volatility (annualized)
            spx_returns = recent_spx.pct_change().dropna()
            
            if len(spx_returns) >= 10:
                rv_10d = spx_returns.tail(10).std() * np.sqrt(252) * 100
                vrp_10d = current_vix - rv_10d
            else:
                vrp_10d = np.nan
            
            if len(spx_returns) >= 20:
                rv_20d = spx_returns.tail(20).std() * np.sqrt(252) * 100
                vrp_20d = current_vix - rv_20d
            else:
                vrp_20d = np.nan
            
            if len(spx_returns) >= 30:
                rv_30d = spx_returns.tail(30).std() * np.sqrt(252) * 100
                vrp_30d = current_vix - rv_30d
            else:
                vrp_30d = np.nan
            
            # Calculate average VRP
            vrp_values = [v for v in [vrp_10d, vrp_20d, vrp_30d] if not np.isnan(v)]
            vrp_avg = np.mean(vrp_values) if vrp_values else np.nan
            
            vrp_results.append({
                'date': date,
                'vrp_10d': vrp_10d,
                'vrp_20d': vrp_20d,
                'vrp_30d': vrp_30d,
                'vrp_avg': vrp_avg
            })
        
        # Convert to DataFrame and merge with VIX data
        vrp_df = pd.DataFrame(vrp_results).set_index('date')
        enhanced_data = pd.merge(vix_data, vrp_df, left_index=True, right_index=True, how='left')
        
        # Count valid VRP calculations
        valid_vrp = enhanced_data['vrp_avg'].notna().sum()
        success_msg = f"Calculated real VRP for {valid_vrp}/{len(enhanced_data)} observations (no forward-looking bias)"
        
        if self.logger:
            self.logger.logger.info(success_msg)
        else:
            print(f"✅ {success_msg}")
        
        if valid_vrp > 0:
            range_msg = f"VRP range: {enhanced_data['vrp_avg'].min():.2f} to {enhanced_data['vrp_avg'].max():.2f}"
            avg_msg = f"Avg VRP: {enhanced_data['vrp_avg'].mean():.2f}"
            
            if self.logger:
                self.logger.logger.info(range_msg)
                self.logger.logger.info(avg_msg)
            else:
                print(f"   {range_msg}")
                print(f"   {avg_msg}")
        
        return enhanced_data

    def load_market_data_with_real_vrp(self) -> Optional[pd.DataFrame]:
        """
        Load market data with real VRP calculation from options data
        Preserves exact logic from final_strategy_clean.py
        """
        if self.logger:
            self.logger.logger.info("Loading market data with real VRP from options data...")
        else:
            print("📊 Loading market data with real VRP from options data...")

        try:
            # Load VIX data using dual file approach (exact original logic)
            vix_df = pd.read_csv(self.vix_data_files['VIX'],
                               names=['date', 'open', 'high', 'low', 'close', 'volume'],
                               parse_dates=['date'])
            vix9d_df = pd.read_csv(self.vix_data_files['VIX9D'],
                                 names=['date', 'open', 'high', 'low', 'close', 'volume'],
                                 parse_dates=['date'])

            # Merge VIX data
            vix_data = pd.merge(vix_df[['date', 'close']],
                              vix9d_df[['date', 'close']],
                              on='date', how='inner', suffixes=('_vix', '_vix9d'))

            # Filter date range
            vix_data = vix_data[
                (vix_data['date'] >= self.start_date) &
                (vix_data['date'] <= self.end_date)
            ].copy()

            # Calculate VIX metrics
            vix_data['vix'] = vix_data['close_vix']
            vix_data['vix9d'] = vix_data['close_vix9d']
            vix_data['vix_momentum'] = vix_data['vix9d'] - vix_data['vix']
            vix_data['vix_momentum_direction'] = np.where(
                vix_data['vix_momentum'] > 0, 'RISING', 'FALLING'
            )

            success_msg = f"Loaded VIX data: {len(vix_data)} records"
            if self.logger:
                self.logger.logger.info(success_msg)
            else:
                print(f"✅ {success_msg}")

            # Load real SPX options data
            self.load_real_spx_options_data()

            # Set index for VRP calculation
            vix_data = vix_data.set_index('date')

            # Calculate real VRP from options data with no forward-looking bias
            enhanced_data = self.calculate_real_vrp_from_options(vix_data)

            final_msg = f"Prepared final market data: {len(enhanced_data)} observations"
            if self.logger:
                self.logger.logger.info(final_msg)
            else:
                print(f"✅ {final_msg}")

            self.market_data = enhanced_data
            return enhanced_data

        except Exception as e:
            error_msg = f"Error loading market data: {e}"
            if self.logger:
                self.logger.logger.error(error_msg)
            else:
                print(f"❌ {error_msg}")
            return None

    def get_options_for_date(self, date: datetime, option_type: str = 'c') -> pd.DataFrame:
        """Get options data for a specific date"""
        if self.spx_options_data is None:
            return pd.DataFrame()

        normalized_date = pd.to_datetime(date).normalize()
        return self.spx_options_data[
            (self.spx_options_data['date'] == normalized_date) &
            (self.spx_options_data['Call/Put'] == option_type)
        ].copy()

    def get_market_data(self) -> Optional[pd.DataFrame]:
        """Get loaded market data"""
        return self.market_data

    def get_spx_options_data(self) -> Optional[pd.DataFrame]:
        """Get loaded SPX options data"""
        return self.spx_options_data
