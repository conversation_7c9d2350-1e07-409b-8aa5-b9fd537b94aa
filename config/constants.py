"""
Constants for JPM Options Trading Strategies
All magic numbers and thresholds extracted from working strategies
"""
from datetime import datetime

# ============================================================================
# DATA PATHS
# ============================================================================
SECURITIES_DATA_DIR = '/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities'
VIX_DATA_PATH = f'{SECURITIES_DATA_DIR}/VIX_full_1day.txt'
VIX9D_DATA_PATH = f'{SECURITIES_DATA_DIR}/VIX9D_full_1day.txt'
SPX_OPTIONS_BASE_PATH = "/Users/<USER>/Downloads/optionhistory"

# ============================================================================
# DATE CONFIGURATION
# ============================================================================
START_DATE = "2023-05-01"
END_DATE = datetime.now().strftime("%Y-%m-%d")  # Auto-update to today

# ============================================================================
# TRADING PARAMETERS
# ============================================================================
INITIAL_CAPITAL = 100000
COMMISSION_PER_CONTRACT = 1.0
COMMISSION_PER_LEG = 2  # Open and close

# ============================================================================
# VIX STRATEGY PARAMETERS
# ============================================================================
VIX_LOW_THRESHOLD = 12.0
VIX_HIGH_THRESHOLD = 22.0
VIX_EXTREME_THRESHOLD = 35.0

# VIX RSI Parameters
VIX_RSI_PERIOD = 2
VIX_RSI_OVERBOUGHT = 95
VIX_RSI_OVERSOLD = 5

# ============================================================================
# VRP STRATEGY PARAMETERS
# ============================================================================
VRP_THRESHOLD = 2.0
VRP_QUALITY_THRESHOLD = 0.3
VRP_HIGH_THRESHOLD = 5.0
VRP_LOW_THRESHOLD = -2.0

# VRP Technical Analysis
VRP_RSI_PERIOD = 14
VRP_RSI_OVERBOUGHT = 70
VRP_RSI_OVERSOLD = 30
VRP_MA_PERIOD = 10

# ============================================================================
# POSITION SIZING PARAMETERS
# ============================================================================
# Single Options Strategy
MIN_CONTRACTS_SINGLE = 5
MAX_CONTRACTS_SINGLE = 20
BASE_CONTRACTS_SINGLE = 15

# Call Spread Strategy  
MIN_CONTRACTS_SPREAD = 20
MAX_CONTRACTS_SPREAD = 60
BASE_CONTRACTS_SPREAD = 44

# Confidence-based sizing multipliers
EXTREME_CONFIDENCE_MULTIPLIER = 1.0  # Full 20 contracts
HIGH_CONFIDENCE_MULTIPLIER = 0.9     # 15-18 contracts
MEDIUM_CONFIDENCE_MULTIPLIER = 0.6   # 10-12 contracts
LOW_CONFIDENCE_MULTIPLIER = 0.4      # 5-8 contracts

# ============================================================================
# STRIKE SELECTION PARAMETERS
# ============================================================================
STRIKE_MULTIPLE = 25  # SPX strikes are multiples of 25
TARGET_STRIKE = 4500  # Default strike for single options

# Call Spread Strike Ranges (as multipliers of SPX price)
BULLISH_SHORT_STRIKE_MIN = 0.97   # 3% ITM
BULLISH_SHORT_STRIKE_MAX = 1.01   # 1% OTM
BULLISH_LONG_STRIKE_MIN = 1.01    # 1% OTM
BULLISH_LONG_STRIKE_MAX = 1.06    # 6% OTM

BEARISH_SHORT_STRIKE_MIN = 1.00   # ATM
BEARISH_SHORT_STRIKE_MAX = 1.02   # 2% OTM
BEARISH_LONG_STRIKE_MIN = 1.02    # 2% OTM
BEARISH_LONG_STRIKE_MAX = 1.07    # 7% OTM

# Call Spread Parameters
CALL_SPREAD_MIN_DTE = 25
CALL_SPREAD_MAX_DTE = 35
CALL_SPREAD_MAX_WIDTH = 150  # Points
CALL_SPREAD_MIN_CREDIT_RATIO = 0.15  # 15% of spread width

# ============================================================================
# OPTION SELECTION CRITERIA
# ============================================================================
MIN_OPTION_PRICE = 0.05
MAX_OPTION_PRICE = 1000.0
MIN_SPX_PRICE = 1000
MAX_SPX_PRICE = 10000

# Data Quality Filters
MIN_VOLUME = 10
MIN_OPEN_INTEREST = 100
MAX_BID_ASK_SPREAD = 0.50

# ============================================================================
# SIGNAL GENERATION PARAMETERS
# ============================================================================
CONFIDENCE_THRESHOLD = 0.5
MIN_SIGNAL_STRENGTH = 0.3
MAX_SIGNALS_PER_DAY = 3

# Confidence Score Thresholds
EXTREME_CONFIDENCE_THRESHOLD = 0.9
HIGH_CONFIDENCE_THRESHOLD = 0.7
MEDIUM_CONFIDENCE_THRESHOLD = 0.5
LOW_CONFIDENCE_THRESHOLD = 0.3

# ============================================================================
# BACKTEST CONFIGURATION
# ============================================================================
DEFAULT_HOLDING_DAYS = 1
MAX_HOLDING_DAYS = 5
LOOKBACK_DAYS = 252  # 1 year

# ============================================================================
# TECHNICAL ANALYSIS PARAMETERS
# ============================================================================
RSI_PERIOD = 14
RSI_OVERBOUGHT = 70
RSI_OVERSOLD = 30
MA_SHORT_PERIOD = 10
MA_LONG_PERIOD = 20

# Volatility Calculations
VOLATILITY_WINDOW = 30
REALIZED_VOL_WINDOW = 21
IMPLIED_VOL_THRESHOLD = 0.15

# ============================================================================
# GREEKS THRESHOLDS
# ============================================================================
MIN_DELTA = 0.10
MAX_DELTA = 0.90
MIN_GAMMA = 0.001
MAX_THETA = -0.10

# ============================================================================
# RISK MANAGEMENT PARAMETERS
# ============================================================================
MAX_DRAWDOWN_THRESHOLD = 0.05  # 5%
STOP_LOSS_THRESHOLD = 0.50     # 50% of premium
MAX_ACCEPTABLE_DRAWDOWN = 0.10

# Performance Thresholds
MIN_WIN_RATE = 0.50
MIN_PROFIT_FACTOR = 2.0

# ============================================================================
# FILE AND DIRECTORY CONFIGURATION
# ============================================================================
REPORTS_DIR = "reports"
TRADES_DIR = "trades"
CHARTS_DIR = "charts"
LOGS_DIR = "logs"

# File Naming Patterns
TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
DATE_FORMAT = "%Y-%m-%d"
LOG_DATE_FORMAT = "%Y%m%d"

# File Extensions
CSV_EXTENSION = ".csv"
PDF_EXTENSION = ".pdf"
PNG_EXTENSION = ".png"
LOG_EXTENSION = ".log"

# ============================================================================
# PDF REPORT CONFIGURATION
# ============================================================================
PDF_TITLE_FONT_SIZE = 16
PDF_HEADER_FONT_SIZE = 14
PDF_BODY_FONT_SIZE = 11

# ============================================================================
# STRATEGY NAMES
# ============================================================================
VRP_STRATEGY_NAME = "Final Real Data VRP Strategy"
CALL_SPREAD_STRATEGY_NAME = "Call Spread Strategy"

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Options Trading Log Columns
OPTIONS_LOG_COLUMNS = [
    'date', 'event_type', 'option_symbol', 'strike', 'expiry', 'price',
    'bid', 'ask', 'volume', 'open_interest', 'delta', 'gamma', 'theta', 'vega',
    'underlying_price', 'days_to_expiry', 'moneyness', 'signal_direction'
]

# Event Types for Options Logging
EVENT_SIGNAL = "SIGNAL"
EVENT_ENTRY = "ENTRY"
EVENT_EXIT = "EXIT"
EVENT_REVALUATION = "REVALUATION"

# ============================================================================
# FALLBACK CALCULATION PARAMETERS
# ============================================================================
FALLBACK_DECAY_RATE = 0.6  # 40% decay per day for fallback calculations
FALLBACK_MIN_PRICE = 0.01  # Minimum option price for fallback

# ============================================================================
# VALIDATION PARAMETERS
# ============================================================================
MIN_TRADES_FOR_VALID_BACKTEST = 10
MIN_DATA_POINTS_FOR_VRP = 30
MAX_MISSING_DATA_PERCENTAGE = 0.1  # 10%

# ============================================================================
# PERFORMANCE BENCHMARKS (for testing)
# ============================================================================
EXPECTED_CALL_SPREAD_RETURN = 7695.0  # 7,695% return
EXPECTED_SINGLE_OPTIONS_RETURN = 7274.0  # 7,274% return
EXPECTED_CALL_SPREAD_WIN_RATE = 61.9
EXPECTED_SINGLE_OPTIONS_WIN_RATE = 43.0
EXPECTED_CALL_SPREAD_TRADES = 189
EXPECTED_SINGLE_OPTIONS_TRADES = 249

# Tolerance for performance validation
PERFORMANCE_TOLERANCE = 0.01  # 1% tolerance for floating point comparisons
