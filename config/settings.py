"""
Settings and Environment Configuration for JPM Options Trading Strategies
Runtime configuration and environment variables
"""
import os
from datetime import datetime
from .constants import *

class Settings:
    """Runtime settings and environment configuration"""
    
    def __init__(self):
        # Environment
        self.environment = os.getenv('ENVIRONMENT', 'development')
        self.debug_mode = os.getenv('DEBUG', 'False').lower() == 'true'
        
        # API Keys
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        # Data Configuration
        self.start_date = os.getenv('START_DATE', START_DATE)
        self.end_date = os.getenv('END_DATE', END_DATE)
        self.vix_data_path = os.getenv('VIX_DATA_PATH', VIX_DATA_PATH)
        self.spx_options_path = os.getenv('SPX_OPTIONS_PATH', SPX_OPTIONS_BASE_PATH)
        
        # Trading Configuration
        self.initial_capital = float(os.getenv('INITIAL_CAPITAL', INITIAL_CAPITAL))
        self.commission_per_contract = float(os.getenv('COMMISSION_PER_CONTRACT', COMMISSION_PER_CONTRACT))
        
        # Strategy Selection
        self.run_call_spread = os.getenv('RUN_CALL_SPREAD', 'true').lower() == 'true'
        self.run_single_options = os.getenv('RUN_SINGLE_OPTIONS', 'true').lower() == 'true'
        
        # Output Configuration
        self.generate_pdf = os.getenv('GENERATE_PDF', 'true').lower() == 'true'
        self.generate_csv = os.getenv('GENERATE_CSV', 'true').lower() == 'true'
        self.verbose_logging = os.getenv('VERBOSE_LOGGING', 'false').lower() == 'true'
        
        # Performance Testing
        self.validate_performance = os.getenv('VALIDATE_PERFORMANCE', 'true').lower() == 'true'
        self.performance_tolerance = float(os.getenv('PERFORMANCE_TOLERANCE', PERFORMANCE_TOLERANCE))
        
        # Ensure directories exist
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [REPORTS_DIR, TRADES_DIR, CHARTS_DIR, LOGS_DIR]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_log_filename(self, strategy_name: str) -> str:
        """Generate log filename for strategy"""
        date_str = datetime.now().strftime(LOG_DATE_FORMAT)
        return f"{LOGS_DIR}/options_trading_log_{strategy_name}_{date_str}.csv"
    
    def get_trade_filename(self, strategy_name: str) -> str:
        """Generate trade filename for strategy"""
        timestamp = datetime.now().strftime(TIMESTAMP_FORMAT)
        return f"{TRADES_DIR}/{strategy_name.lower().replace(' ', '_')}_trades_{timestamp}.csv"
    
    def get_report_filename(self, strategy_name: str) -> str:
        """Generate report filename for strategy"""
        timestamp = datetime.now().strftime(TIMESTAMP_FORMAT)
        return f"{REPORTS_DIR}/{strategy_name.lower().replace(' ', '_')}_report_{timestamp}.pdf"
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == 'production'
    
    def should_validate_performance(self) -> bool:
        """Check if performance validation is enabled"""
        return self.validate_performance and not self.is_production()

# Global settings instance
settings = Settings()
