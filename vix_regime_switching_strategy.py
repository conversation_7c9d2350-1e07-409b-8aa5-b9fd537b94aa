#!/usr/bin/env python3
"""
VIX-Based Regime Switching Strategy
Switches between call spreads and put spreads based on VIX levels
- Low VIX: Use call spreads (capture overnight bullish bias)
- High VIX: Use put spreads (protect against volatility spikes)
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta

class VIXRegimeSwitchingStrategy:
    """
    VIX-based regime switching strategy
    - VIX < 15: Call spreads (bullish overnight bias)
    - VIX >= 15: Put spreads (bearish protection)
    """
    
    def __init__(self):
        self.spx_options_data = None
        self.vix_data = None
        self.spread_width = 150
        self.otm_offset = 25
        self.vix_threshold = 15.0  # Based on analysis - all profitable trades were VIX < 15
        
    def load_spx_options_data(self):
        """Load SPX options data"""
        print("📊 Loading SPX options data...")
        
        options_dir = "/Users/<USER>/Downloads/optionhistory"
        if not os.path.exists(options_dir):
            return None

        all_files = []
        for year in range(2023, 2026):
            for quarter in range(1, 5):
                pattern = f"{options_dir}/{year}_q{quarter}_option_chain/spx_complete_{year}_q{quarter}.csv"
                files = glob.glob(pattern)
                all_files.extend(files)

        if not all_files:
            return None

        all_data = []
        for file in all_files:
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"   ✅ Loaded {len(df)} records from {os.path.basename(file)}")
            except Exception as e:
                print(f"   ❌ Error loading {file}: {e}")

        if not all_data:
            return None

        combined_data = pd.concat(all_data, ignore_index=True)
        combined_data['date'] = pd.to_datetime(combined_data['date'])
        combined_data['expiry_date'] = pd.to_datetime(combined_data['Expiry Date'])
        combined_data['option_price'] = combined_data['Last Trade Price']
        
        combined_data = combined_data.dropna(subset=['option_price', 'Strike', 'spx_close'])
        combined_data = combined_data[combined_data['option_price'] > 0]
        
        print(f"✅ Combined SPX options data: {len(combined_data)} records")
        return combined_data

    def create_vix_proxy(self):
        """Create VIX proxy from options data"""
        print("📊 Creating VIX proxy...")
        
        # Get daily SPX data
        daily_data = self.spx_options_data.groupby('date').agg({
            'spx_close': 'first'
        }).reset_index().sort_values('date')
        
        vix_proxy_data = []
        unique_dates = sorted(self.spx_options_data['date'].unique())
        
        for i, date in enumerate(unique_dates[::10]):  # Sample every 10th date for speed
            day_data = self.spx_options_data[self.spx_options_data['date'] == date].copy()
            if len(day_data) == 0:
                continue
                
            spx_price = day_data['spx_close'].iloc[0]
            
            # Get ATM options with 20-40 days to expiry
            day_data.loc[:, 'days_to_expiry'] = (day_data['expiry_date'] - day_data['date']).dt.days
            atm_options = day_data[
                (day_data['days_to_expiry'] >= 20) & 
                (day_data['days_to_expiry'] <= 40) &
                (day_data['option_price'] > 0)
            ].copy()
            
            if len(atm_options) == 0:
                continue
                
            # Find options close to ATM
            atm_options.loc[:, 'strike_distance'] = abs(atm_options['Strike'] - spx_price)
            atm_options = atm_options[atm_options['strike_distance'] <= 100]
            
            if len(atm_options) == 0:
                continue
                
            # Calculate implied volatility proxy
            atm_options.loc[:, 'iv_proxy'] = (atm_options['option_price'] / spx_price) * np.sqrt(365 / atm_options['days_to_expiry']) * 100
            
            # VIX proxy is the median IV
            vix_proxy = atm_options['iv_proxy'].median()
            
            if pd.isna(vix_proxy) or vix_proxy <= 0:
                continue
                
            vix_proxy_data.append({
                'date': date,
                'vix_close': vix_proxy
            })
        
        if len(vix_proxy_data) > 0:
            self.vix_data = pd.DataFrame(vix_proxy_data)
            print(f"✅ Created VIX proxy: {len(self.vix_data)} records")
            return True
        else:
            print("❌ Failed to create VIX proxy")
            return False

    def find_spread_options(self, date, spx_price, strategy_type):
        """
        Find spread options based on strategy type
        strategy_type: 'CALL' or 'PUT'
        """
        
        # Get options for this date
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == date) &
            (self.spx_options_data['Call/Put'] == ('c' if strategy_type == 'CALL' else 'p'))
        ].copy()

        if len(day_options) == 0:
            return None, None

        day_options['expiry_date'] = pd.to_datetime(day_options['expiry_date'])
        day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days
        day_options = day_options[day_options['days_to_expiry'] >= 20].copy()
        
        if len(day_options) == 0:
            return None, None

        # Round SPX to nearest 25
        quotient = spx_price / 25
        remainder = quotient - int(quotient)
        spx_rounded = (int(quotient) + 1) * 25 if remainder > 0.5 else int(quotient) * 25

        if strategy_type == 'CALL':
            # CALL SPREAD: Buy call 25 points OTM, sell call 150 points further out
            long_strike = spx_rounded + self.otm_offset
            short_strike = long_strike + self.spread_width
        else:  # PUT
            # PUT SPREAD: Buy put 25 points OTM, sell put 150 points further out
            long_strike = spx_rounded - self.otm_offset
            short_strike = long_strike - self.spread_width

        # Find options
        long_option = day_options.iloc[(day_options['Strike'] - long_strike).abs().argsort()[:1]]
        short_option = day_options.iloc[(day_options['Strike'] - short_strike).abs().argsort()[:1]]

        if len(long_option) == 0 or len(short_option) == 0:
            return None, None

        long_option = long_option.iloc[0]
        short_option = short_option.iloc[0]

        if long_option['option_price'] <= 0 or short_option['option_price'] <= 0:
            return None, None

        return long_option, short_option

    def get_next_trading_day(self, date):
        """Get next trading day"""
        next_day = date + timedelta(days=1)
        while next_day.weekday() >= 5:
            next_day += timedelta(days=1)
        return next_day

    def execute_regime_switching_strategy(self):
        """Execute VIX-based regime switching strategy"""
        print("🚀 EXECUTING VIX-BASED REGIME SWITCHING STRATEGY")
        print("=" * 60)
        print(f"📋 VIX < {self.vix_threshold}: Call spreads (bullish overnight)")
        print(f"📋 VIX >= {self.vix_threshold}: Put spreads (bearish protection)")
        print("=" * 60)

        # Load data
        self.spx_options_data = self.load_spx_options_data()
        if self.spx_options_data is None:
            return None

        if not self.create_vix_proxy():
            return None

        trades = []
        successful_trades = 0
        failed_trades = 0
        call_trades = 0
        put_trades = 0

        # Test on every 3rd day (like simple strategy)
        unique_dates = sorted(self.spx_options_data['date'].unique())
        test_dates = unique_dates[::3]
        
        # Filter for dates with VIX data
        vix_dates = set(self.vix_data['date'])
        test_dates = [d for d in test_dates if d in vix_dates]
        
        print(f"📊 Testing on {len(test_dates)} dates")

        for date in test_dates:
            # Get VIX for regime determination
            vix_row = self.vix_data[self.vix_data['date'] == date]
            if len(vix_row) == 0:
                failed_trades += 1
                continue
                
            vix = vix_row['vix_close'].iloc[0]
            spx_price = self.spx_options_data[self.spx_options_data['date'] == date]['spx_close'].iloc[0]

            # REGIME SWITCHING LOGIC
            if vix < self.vix_threshold:
                strategy_type = 'CALL'  # Low VIX: Use call spreads
                call_trades += 1
            else:
                strategy_type = 'PUT'   # High VIX: Use put spreads
                put_trades += 1

            # Find options
            long_option, short_option = self.find_spread_options(date, spx_price, strategy_type)
            if long_option is None or short_option is None:
                failed_trades += 1
                continue

            # Calculate spread
            long_premium = long_option['option_price']
            short_premium = short_option['option_price']
            net_debit = long_premium - short_premium

            # Simple filters
            if net_debit <= 5.0 or net_debit >= 50.0:
                failed_trades += 1
                continue

            # Fixed position size
            contracts = 5

            # Get exit prices
            exit_date = self.get_next_trading_day(date)

            exit_long_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == long_option['Strike']) &
                (self.spx_options_data['expiry_date'] == long_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == long_option['Call/Put'])
            ]

            exit_short_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == short_option['Strike']) &
                (self.spx_options_data['expiry_date'] == short_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == short_option['Call/Put'])
            ]

            if len(exit_long_options) == 0 or len(exit_short_options) == 0:
                failed_trades += 1
                continue

            exit_long_price = exit_long_options['option_price'].iloc[0]
            exit_short_price = exit_short_options['option_price'].iloc[0]

            # Calculate P&L
            long_pnl = (exit_long_price - long_premium) * contracts * 100
            short_pnl = (short_premium - exit_short_price) * contracts * 100
            total_pnl = long_pnl + short_pnl

            commission = contracts * 2 * 2
            net_pnl = total_pnl - commission

            exit_spx_price = exit_long_options['spx_close'].iloc[0]
            spx_change = exit_spx_price - spx_price

            # Create trade record
            trade = {
                'date': date,
                'exit_date': exit_date,
                'spx_entry': spx_price,
                'spx_exit': exit_spx_price,
                'spx_change': spx_change,
                'vix': vix,
                'strategy_type': strategy_type,
                'long_strike': long_option['Strike'],
                'short_strike': short_option['Strike'],
                'long_entry_price': long_premium,
                'short_entry_price': short_premium,
                'net_debit': net_debit,
                'long_exit_price': exit_long_price,
                'short_exit_price': exit_short_price,
                'contracts': contracts,
                'long_pnl': long_pnl,
                'short_pnl': short_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0
            }

            trades.append(trade)
            successful_trades += 1

            # Show first few trades
            if successful_trades <= 5:
                print(f"✅ Trade {successful_trades}: VIX {vix:.1f} → {strategy_type} spread, "
                      f"SPX {spx_price:.0f}→{exit_spx_price:.0f} ({spx_change:+.0f}), P&L ${net_pnl:+,.0f}")

        # Results
        if trades:
            trades_df = pd.DataFrame(trades)
            
            os.makedirs('trades', exist_ok=True)
            trades_df.to_csv('trades/vix_regime_switching_trades.csv', index=False)
            
            total_pnl = trades_df['net_pnl'].sum()
            win_rate = trades_df['win_loss_flag'].mean() * 100
            total_return = (total_pnl / 100000) * 100
            
            winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
            losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
            
            avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
            avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
            
            print(f"\n🎯 VIX REGIME SWITCHING STRATEGY PERFORMANCE")
            print("=" * 55)
            print(f"📊 Total Trades: {len(trades_df)}")
            print(f"📞 Call Spread Trades: {call_trades}")
            print(f"📉 Put Spread Trades: {put_trades}")
            print(f"🎯 Win Rate: {win_rate:.1f}%")
            print(f"💰 Total P&L: ${total_pnl:+,.0f}")
            print(f"📈 Total Return: {total_return:+.1f}%")
            print(f"💪 Average Win: ${avg_win:+,.0f}")
            print(f"💔 Average Loss: ${avg_loss:+,.0f}")
            print(f"📊 Average VIX: {trades_df['vix'].mean():.1f}")
            print(f"📊 SPX Up Days: {(trades_df['spx_change'] > 0).sum()}/{len(trades_df)} ({(trades_df['spx_change'] > 0).mean()*100:.1f}%)")
            
            # Performance by strategy type
            call_performance = trades_df[trades_df['strategy_type'] == 'CALL']
            put_performance = trades_df[trades_df['strategy_type'] == 'PUT']
            
            if len(call_performance) > 0:
                call_pnl = call_performance['net_pnl'].sum()
                call_win_rate = call_performance['win_loss_flag'].mean() * 100
                print(f"\n📞 CALL SPREAD PERFORMANCE:")
                print(f"   Trades: {len(call_performance)}, P&L: ${call_pnl:+,.0f}, Win Rate: {call_win_rate:.1f}%")
            
            if len(put_performance) > 0:
                put_pnl = put_performance['net_pnl'].sum()
                put_win_rate = put_performance['win_loss_flag'].mean() * 100
                print(f"📉 PUT SPREAD PERFORMANCE:")
                print(f"   Trades: {len(put_performance)}, P&L: ${put_pnl:+,.0f}, Win Rate: {put_win_rate:.1f}%")
            
            return trades_df
        else:
            print("❌ No successful trades")
            return None


if __name__ == "__main__":
    strategy = VIXRegimeSwitchingStrategy()
    results = strategy.execute_regime_switching_strategy()
    
    if results is not None:
        print(f"\n✅ VIX regime switching strategy completed!")
        print(f"📊 Check trades/vix_regime_switching_trades.csv for results")
