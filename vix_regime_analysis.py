#!/usr/bin/env python3
"""
VIX-Based Regime Analysis for Overnight Strategy
Analyze simple strategy results and implement VIX-based regime switching
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

class VIXRegimeAnalyzer:
    """
    Analyze VIX patterns in simple strategy results and implement regime switching
    """
    
    def __init__(self):
        self.simple_trades = None
        self.vix_data = None
        self.spx_options_data = None
        
    def load_simple_strategy_results(self):
        """Load the simple overnight strategy results"""
        print("📊 LOADING SIMPLE STRATEGY RESULTS")
        print("=" * 50)
        
        try:
            self.simple_trades = pd.read_csv('trades/simple_overnight_trades.csv')
            self.simple_trades['date'] = pd.to_datetime(self.simple_trades['date'])
            self.simple_trades['exit_date'] = pd.to_datetime(self.simple_trades['exit_date'])
            
            print(f"✅ Loaded {len(self.simple_trades)} trades from simple strategy")
            print(f"   Date range: {self.simple_trades['date'].min().strftime('%Y-%m-%d')} to {self.simple_trades['date'].max().strftime('%Y-%m-%d')}")
            print(f"   Total P&L: ${self.simple_trades['net_pnl'].sum():+,.0f}")
            print(f"   Win Rate: {self.simple_trades['win_loss_flag'].mean()*100:.1f}%")
            
            return True
            
        except FileNotFoundError:
            print("❌ Simple strategy results not found. Run simple_overnight_strategy.py first.")
            return False
    
    def load_vix_data(self):
        """Load VIX data or create proxy from options data"""
        print("\n📊 LOADING VIX DATA")
        print("-" * 30)
        
        # Try to load real VIX data first
        vix_file = "/Users/<USER>/Downloads/optionhistory/vix_data.csv"
        if os.path.exists(vix_file):
            try:
                self.vix_data = pd.read_csv(vix_file)
                self.vix_data['date'] = pd.to_datetime(self.vix_data['date'])
                print(f"✅ Loaded real VIX data: {len(self.vix_data)} records")
                return True
            except Exception as e:
                print(f"⚠️ Error loading VIX file: {e}")
        
        # Create VIX proxy from options data
        print("📊 Creating VIX proxy from options data...")
        
        # Load SPX options data
        options_dir = "/Users/<USER>/Downloads/optionhistory"
        if not os.path.exists(options_dir):
            print("❌ Options directory not found")
            return False
            
        # Load options data
        all_files = []
        for year in range(2023, 2026):
            for quarter in range(1, 5):
                pattern = f"{options_dir}/{year}_q{quarter}_option_chain/spx_complete_{year}_q{quarter}.csv"
                files = glob.glob(pattern)
                all_files.extend(files)
        
        if not all_files:
            print("❌ No options files found")
            return False
            
        print(f"📊 Loading {len(all_files)} options files for VIX proxy...")
        
        all_data = []
        for file in all_files[:3]:  # Limit to first 3 files for speed
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"   ✅ Loaded {os.path.basename(file)}")
            except Exception as e:
                print(f"   ❌ Error loading {file}: {e}")
        
        if not all_data:
            return False
            
        combined_data = pd.concat(all_data, ignore_index=True)
        combined_data['date'] = pd.to_datetime(combined_data['date'])
        combined_data['expiry_date'] = pd.to_datetime(combined_data['Expiry Date'])
        combined_data['option_price'] = combined_data['Last Trade Price']
        
        # Create VIX proxy from ATM options
        vix_proxy_data = []
        
        unique_dates = sorted(combined_data['date'].unique())
        print(f"📊 Calculating VIX proxy for {len(unique_dates)} dates...")
        
        for i, date in enumerate(unique_dates):
            if i % 50 == 0:
                print(f"   Processing {i}/{len(unique_dates)} dates...")
                
            day_data = combined_data[combined_data['date'] == date]
            if len(day_data) == 0:
                continue
                
            spx_price = day_data['spx_close'].iloc[0]
            
            # Get ATM options with 20-40 days to expiry
            day_data['days_to_expiry'] = (day_data['expiry_date'] - day_data['date']).dt.days
            atm_options = day_data[
                (day_data['days_to_expiry'] >= 20) & 
                (day_data['days_to_expiry'] <= 40) &
                (day_data['option_price'] > 0)
            ].copy()
            
            if len(atm_options) == 0:
                continue
                
            # Find options close to ATM
            atm_options['strike_distance'] = abs(atm_options['Strike'] - spx_price)
            atm_options = atm_options[atm_options['strike_distance'] <= 100]
            
            if len(atm_options) == 0:
                continue
                
            # Calculate implied volatility proxy
            atm_options['iv_proxy'] = (atm_options['option_price'] / spx_price) * np.sqrt(365 / atm_options['days_to_expiry']) * 100
            
            # VIX proxy is the median IV
            vix_proxy = atm_options['iv_proxy'].median()
            
            if pd.isna(vix_proxy) or vix_proxy <= 0:
                continue
                
            vix_proxy_data.append({
                'date': date,
                'vix_close': vix_proxy
            })
        
        if len(vix_proxy_data) > 0:
            self.vix_data = pd.DataFrame(vix_proxy_data)
            print(f"✅ Created VIX proxy: {len(self.vix_data)} records")
            print(f"   VIX range: {self.vix_data['vix_close'].min():.1f} to {self.vix_data['vix_close'].max():.1f}")
            print(f"   Average VIX: {self.vix_data['vix_close'].mean():.1f}")
            return True
        else:
            print("❌ Failed to create VIX proxy")
            return False
    
    def analyze_vix_loss_patterns(self):
        """Analyze which VIX levels correlate with losses"""
        print("\n📊 ANALYZING VIX LOSS PATTERNS")
        print("-" * 40)
        
        # Merge trades with VIX data
        trades_with_vix = pd.merge(
            self.simple_trades, 
            self.vix_data, 
            on='date', 
            how='inner'
        )
        
        if len(trades_with_vix) == 0:
            print("❌ No trades matched with VIX data")
            return None
            
        print(f"✅ Matched {len(trades_with_vix)} trades with VIX data")
        
        # Define VIX regimes
        trades_with_vix['vix_regime'] = pd.cut(
            trades_with_vix['vix_close'], 
            bins=[0, 15, 20, 25, 30, 100], 
            labels=['Very Low (<15)', 'Low (15-20)', 'Medium (20-25)', 'High (25-30)', 'Very High (>30)']
        )
        
        # Analyze performance by VIX regime
        regime_analysis = trades_with_vix.groupby('vix_regime').agg({
            'net_pnl': ['count', 'sum', 'mean'],
            'win_loss_flag': 'mean',
            'spx_change': 'mean',
            'vix_close': 'mean'
        }).round(2)
        
        print("\n📊 PERFORMANCE BY VIX REGIME:")
        print("=" * 80)
        print(f"{'Regime':<15} {'Trades':<8} {'Total P&L':<12} {'Avg P&L':<10} {'Win Rate':<10} {'Avg SPX Δ':<12} {'Avg VIX':<10}")
        print("-" * 80)
        
        regime_results = {}
        
        for regime in regime_analysis.index:
            if pd.isna(regime):
                continue
                
            trade_count = int(regime_analysis.loc[regime, ('net_pnl', 'count')])
            total_pnl = regime_analysis.loc[regime, ('net_pnl', 'sum')]
            avg_pnl = regime_analysis.loc[regime, ('net_pnl', 'mean')]
            win_rate = regime_analysis.loc[regime, ('win_loss_flag', 'mean')] * 100
            avg_spx_change = regime_analysis.loc[regime, ('spx_change', 'mean')]
            avg_vix = regime_analysis.loc[regime, ('vix_close', 'mean')]
            
            print(f"{str(regime):<15} {trade_count:<8} ${total_pnl:<11,.0f} ${avg_pnl:<9,.0f} {win_rate:<9.1f}% {avg_spx_change:<11.1f} {avg_vix:<9.1f}")
            
            regime_results[str(regime)] = {
                'trades': trade_count,
                'total_pnl': total_pnl,
                'avg_pnl': avg_pnl,
                'win_rate': win_rate,
                'avg_spx_change': avg_spx_change,
                'avg_vix': avg_vix,
                'profitable': avg_pnl > 0
            }
        
        # Identify regimes where call spreads lose money
        losing_regimes = [regime for regime, data in regime_results.items() if data['avg_pnl'] < 0]
        winning_regimes = [regime for regime, data in regime_results.items() if data['avg_pnl'] > 0]
        
        print(f"\n💡 REGIME ANALYSIS INSIGHTS:")
        print(f"   📈 Call spreads profitable in: {', '.join(winning_regimes) if winning_regimes else 'None'}")
        print(f"   📉 Call spreads lose money in: {', '.join(losing_regimes) if losing_regimes else 'None'}")
        
        # Determine VIX threshold for regime switching
        vix_threshold = None
        if losing_regimes:
            # Find the VIX level that separates winning from losing regimes
            losing_vix_levels = [regime_results[regime]['avg_vix'] for regime in losing_regimes]
            winning_vix_levels = [regime_results[regime]['avg_vix'] for regime in winning_regimes]
            
            if losing_vix_levels and winning_vix_levels:
                min_losing_vix = min(losing_vix_levels)
                max_winning_vix = max(winning_vix_levels)
                vix_threshold = (min_losing_vix + max_winning_vix) / 2
                
                print(f"   🎯 Suggested VIX threshold for regime switching: {vix_threshold:.1f}")
                print(f"      Use CALL spreads when VIX < {vix_threshold:.1f}")
                print(f"      Use PUT spreads when VIX >= {vix_threshold:.1f}")
        
        return trades_with_vix, regime_results, vix_threshold
    
    def run_analysis(self):
        """Run the complete VIX regime analysis"""
        print("🎯 VIX-BASED REGIME ANALYSIS FOR OVERNIGHT STRATEGY")
        print("=" * 60)
        
        # Load data
        if not self.load_simple_strategy_results():
            return None
            
        if not self.load_vix_data():
            return None
            
        # Analyze patterns
        analysis_results = self.analyze_vix_loss_patterns()
        
        if analysis_results is None:
            return None
            
        trades_with_vix, regime_results, vix_threshold = analysis_results
        
        print(f"\n✅ VIX REGIME ANALYSIS COMPLETE")
        print("=" * 40)
        print(f"📊 Analyzed {len(trades_with_vix)} trades across VIX regimes")
        
        if vix_threshold:
            print(f"🎯 Recommended VIX threshold: {vix_threshold:.1f}")
            print(f"📋 Next step: Implement regime switching strategy")
        else:
            print("⚠️ No clear VIX threshold identified")
            
        return {
            'trades_with_vix': trades_with_vix,
            'regime_results': regime_results,
            'vix_threshold': vix_threshold
        }


if __name__ == "__main__":
    analyzer = VIXRegimeAnalyzer()
    results = analyzer.run_analysis()

    if results:
        print(f"\n🚀 Ready to implement VIX-based regime switching strategy!")

        # Save analysis results for regime switching strategy
        import pickle
        with open('vix_analysis_results.pkl', 'wb') as f:
            pickle.dump(results, f)
        print("📊 Analysis results saved for regime switching implementation")
    else:
        print("❌ Analysis failed")
