# JPM Options Trading Strategies - Automation Guide

## 🚀 **Quick Start**

### **1. One-Command Execution**
```bash
./run_strategies.sh
```

This single command will:
- ✅ Detect and activate your Python virtual environment (.venv)
- ✅ Load environment variables from .env file
- ✅ Install missing Python packages automatically
- ✅ Execute both call spread and single options strategies
- ✅ Generate comprehensive PDF reports with charts
- ✅ Display performance summary

## 📋 **Prerequisites**

### **Required Files**
- `run_strategies.sh` - Main automation script
- `.env` - Environment variables (created from template if missing)
- `requirements.txt` - Python dependencies
- Strategy files: `call_spread_strategy.py`, `final_strategy_clean.py`

### **Python Environment**
- Python 3.7+ installed
- Virtual environment recommended (`.venv` or `venv`)

## ⚙️ **Environment Setup**

### **1. Virtual Environment (Recommended)**
```bash
# Create virtual environment
python3 -m venv .venv

# Activate (Linux/Mac)
source .venv/bin/activate

# Activate (Windows)
.venv\Scripts\activate
```

### **2. Environment Variables**
Copy `.env.template` to `.env` and customize:

```bash
cp .env.template .env
```

**Key Settings:**
```bash
# OpenAI API Key (optional - for enhanced PDF narratives)
OPENAI_API_KEY=your_api_key_here

# Strategy Configuration
STARTING_CAPITAL=100000
MAX_POSITION_SIZE=25

# Reporting
GENERATE_PDF_REPORTS=true
INCLUDE_CHARTS=true
```

## 🎯 **Script Features**

### **Automatic Environment Detection**
- ✅ Detects `.venv` or `venv` directories
- ✅ Activates virtual environment automatically
- ✅ Offers to create virtual environment if missing
- ✅ Uses correct pip command (pip vs pip3)

### **Smart Package Management**
- ✅ Checks for required Python packages
- ✅ Installs from `requirements.txt` if available
- ✅ Falls back to individual package installation
- ✅ Upgrades pip automatically

### **Comprehensive Execution**
- ✅ **Call Spread Strategy**: Two-leg options with defined risk
- ✅ **Single Options Strategy**: Directional trading with VRP signals
- ✅ **PDF Reports**: Charts, narratives, and analysis
- ✅ **Performance Summary**: Win rates, returns, drawdowns

## 📊 **Expected Output**

### **Call Spread Strategy Results**
```
📊 CALL SPREAD RESULTS:
  📈 Total Return: +1,352.4%
  🎯 Win Rate: 98.2%
  📊 Total Trades: 56
  💰 Total P&L: $1,352,390
```

### **Single Options Strategy Results**
```
📊 SINGLE OPTIONS RESULTS:
  📈 Total Return: +4,323.3%
  🎯 Win Rate: 90.0%
  📊 Total Trades: 249
  💰 Total P&L: $4,323,288
```

### **Generated Files**
```
📁 reports/
  ├── Call_Spread_Strategy_Report_[timestamp].pdf
  ├── Final_Real_Data_Strategy_Report_[timestamp].pdf
  └── *.png (charts)

📁 trades/
  ├── call_spread_trades.csv
  └── trades_analysis.csv
```

## 🛠 **Troubleshooting**

### **Virtual Environment Issues**
```bash
# If .venv not detected, create manually:
python3 -m venv .venv
source .venv/bin/activate

# Then run script:
./run_strategies.sh
```

### **Package Installation Issues**
```bash
# Install packages manually:
pip install -r requirements.txt

# Or install individually:
pip install pandas numpy matplotlib seaborn reportlab openai
```

### **Permission Issues**
```bash
# Make script executable:
chmod +x run_strategies.sh
```

### **OpenAI API Issues**
- Script works without OpenAI API key (uses default narratives)
- Get API key from: https://platform.openai.com/api-keys
- Add to .env file: `OPENAI_API_KEY=your_key_here`

## 🔧 **Customization**

### **Strategy Parameters**
Edit `.env` file to customize:
- Position sizes
- Risk thresholds
- Data paths
- Reporting options

### **Script Behavior**
Edit `run_strategies.sh` to:
- Change execution order
- Add custom validation
- Modify output format
- Add additional strategies

## 📈 **Performance Monitoring**

The script provides comprehensive performance tracking:

### **Real-Time Monitoring**
- Strategy execution progress
- Package installation status
- Error detection and reporting

### **Final Summary**
- Complete performance metrics
- Generated file locations
- Execution time and status

## 🎯 **Best Practices**

1. **Use Virtual Environment**: Isolates dependencies
2. **Set OpenAI API Key**: Enables enhanced PDF narratives
3. **Review .env Settings**: Customize for your needs
4. **Check Generated Reports**: Verify results in PDF files
5. **Monitor Trade Files**: Analyze CSV data for insights

## 🚀 **Advanced Usage**

### **Batch Execution**
```bash
# Run multiple times with different settings
for config in config1.env config2.env; do
    cp $config .env
    ./run_strategies.sh
done
```

### **Automated Scheduling**
```bash
# Add to crontab for daily execution
0 9 * * 1-5 cd /path/to/strategy && ./run_strategies.sh
```

### **Custom Reporting**
```bash
# Generate only PDFs without strategy execution
GENERATE_PDF_REPORTS=true ./run_strategies.sh
```

## 📞 **Support**

For issues or questions:
1. Check this guide first
2. Review error messages in terminal output
3. Verify all required files are present
4. Ensure Python environment is properly set up
