#!/usr/bin/env python3
"""
Simple Overnight Call Spread Strategy
Buy call 25 points OTM, sell call 150 points further out
Hold overnight, close next day to capture overnight S&P gains
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta

class SimpleOvernightStrategy:
    """
    Simple overnight call spread strategy
    - Buy call 25 points OTM at close
    - Sell call 150 points further out (175 points OTM total)
    - Hold overnight, close next day
    """
    
    def __init__(self):
        self.spx_options_data = None
        self.spread_width = 150  # Points between strikes
        self.otm_offset = 25     # Points OTM for long call
        
    def load_spx_options_data(self):
        """Load SPX options data from CSV files"""
        print("📊 Loading SPX options data...")
        
        options_dir = "/Users/<USER>/Downloads/optionhistory"
        if not os.path.exists(options_dir):
            print(f"❌ Options directory not found: {options_dir}")
            return None

        # Find all SPX options files
        all_files = []
        for year in range(2023, 2026):
            for quarter in range(1, 5):
                pattern = f"{options_dir}/{year}_q{quarter}_option_chain/spx_complete_{year}_q{quarter}.csv"
                files = glob.glob(pattern)
                all_files.extend(files)

        if not all_files:
            print("❌ No SPX options files found")
            return None

        print(f"📊 Found {len(all_files)} SPX options files")

        # Load and combine all files
        all_data = []
        for file in all_files:
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"   ✅ Loaded {len(df)} records from {os.path.basename(file)}")
            except Exception as e:
                print(f"   ❌ Error loading {file}: {e}")

        if not all_data:
            return None

        # Combine all data
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # Convert date columns
        combined_data['date'] = pd.to_datetime(combined_data['date'])
        combined_data['expiry_date'] = pd.to_datetime(combined_data['Expiry Date'])
        
        # Use Last Trade Price as the option price
        combined_data['option_price'] = combined_data['Last Trade Price']
        
        # Filter for valid data
        combined_data = combined_data.dropna(subset=['option_price', 'Strike', 'spx_close'])
        combined_data = combined_data[combined_data['option_price'] > 0]
        
        # Filter for calls only
        combined_data = combined_data[combined_data['Call/Put'] == 'c']
        
        print(f"✅ Combined SPX call options data: {len(combined_data)} records")
        print(f"   Date range: {combined_data['date'].min()} to {combined_data['date'].max()}")
        print(f"   SPX range: {combined_data['spx_close'].min():.0f} to {combined_data['spx_close'].max():.0f}")
        
        return combined_data

    def find_call_spread_options(self, date, spx_price):
        """
        Find call spread options for overnight strategy
        Buy call 25 points OTM, sell call 150 points further out
        """
        
        # Get call options for this date
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == date) &
            (self.spx_options_data['Call/Put'] == 'c')
        ].copy()

        if len(day_options) == 0:
            return None, None

        # Calculate days to expiry
        day_options['expiry_date'] = pd.to_datetime(day_options['expiry_date'])
        day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days

        # Filter for options with 20+ days to expiry
        day_options = day_options[day_options['days_to_expiry'] >= 20].copy()
        if len(day_options) == 0:
            return None, None

        # Round SPX to nearest 25
        quotient = spx_price / 25
        remainder = quotient - int(quotient)
        spx_rounded = (int(quotient) + 1) * 25 if remainder > 0.5 else int(quotient) * 25

        # SIMPLE STRIKE SELECTION
        # Buy call 25 points OTM
        long_strike = spx_rounded + self.otm_offset
        # Sell call 150 points further out (175 points OTM total)
        short_strike = long_strike + self.spread_width

        # Find closest available strikes
        long_option = day_options.iloc[(day_options['Strike'] - long_strike).abs().argsort()[:1]]
        short_option = day_options.iloc[(day_options['Strike'] - short_strike).abs().argsort()[:1]]

        if len(long_option) == 0 or len(short_option) == 0:
            return None, None

        # Ensure we have valid options with prices
        long_option = long_option.iloc[0]
        short_option = short_option.iloc[0]

        if long_option['option_price'] <= 0 or short_option['option_price'] <= 0:
            return None, None

        return long_option, short_option

    def get_next_trading_day(self, date):
        """Get the next trading day (simple weekend logic)"""
        next_day = date + timedelta(days=1)
        while next_day.weekday() >= 5:  # Skip weekends
            next_day += timedelta(days=1)
        return next_day

    def execute_simple_strategy(self):
        """Execute the simple overnight call spread strategy"""
        print("🚀 EXECUTING SIMPLE OVERNIGHT CALL SPREAD STRATEGY")
        print("=" * 60)
        print("📋 Strategy: Buy call 25 pts OTM, sell call 150 pts further out")
        print("📋 Hold overnight, close next day")
        print("=" * 60)

        # Load options data
        self.spx_options_data = self.load_spx_options_data()
        if self.spx_options_data is None:
            print("❌ Strategy execution failed - no options data available")
            return None

        # Get unique trading dates
        unique_dates = sorted(self.spx_options_data['date'].unique())
        print(f"📊 Found {len(unique_dates)} trading dates")

        trades = []
        successful_trades = 0
        failed_trades = 0

        # Test on every 3rd day to get a larger sample
        test_dates = unique_dates[::3]  # Every 3rd day
        print(f"📊 Testing on {len(test_dates)} sample dates")

        for date in test_dates:
            # Get SPX price for this date
            day_data = self.spx_options_data[self.spx_options_data['date'] == date]
            if len(day_data) == 0:
                failed_trades += 1
                continue

            spx_price = day_data['spx_close'].iloc[0]

            # Find call spread options
            long_option, short_option = self.find_call_spread_options(date, spx_price)
            if long_option is None or short_option is None:
                failed_trades += 1
                continue

            # Calculate spread parameters
            long_premium = long_option['option_price']   # We pay this (buy)
            short_premium = short_option['option_price'] # We receive this (sell)
            net_debit = long_premium - short_premium     # Net debit paid

            # Skip if debit is too large (> $50) or too small (< $5)
            if net_debit <= 5.0 or net_debit >= 50.0:
                failed_trades += 1
                continue

            # Use small position size (5 contracts)
            contracts = 5

            # Calculate exit date (next trading day)
            exit_date = self.get_next_trading_day(date)

            # Find exit prices
            exit_long_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == long_option['Strike']) &
                (self.spx_options_data['expiry_date'] == long_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            exit_short_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == short_option['Strike']) &
                (self.spx_options_data['expiry_date'] == short_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            if len(exit_long_options) == 0 or len(exit_short_options) == 0:
                failed_trades += 1
                continue

            exit_long_price = exit_long_options['option_price'].iloc[0]
            exit_short_price = exit_short_options['option_price'].iloc[0]

            # Calculate P&L
            long_pnl = (exit_long_price - long_premium) * contracts * 100   # We bought, so profit if price goes up
            short_pnl = (short_premium - exit_short_price) * contracts * 100 # We sold, so profit if price goes down
            total_pnl = long_pnl + short_pnl

            # Commission (simple estimate)
            commission = contracts * 2 * 2  # $2 per contract per leg
            net_pnl = total_pnl - commission

            # Get exit SPX price
            exit_spx_price = exit_long_options['spx_close'].iloc[0]
            spx_change = exit_spx_price - spx_price

            # Create trade record
            trade = {
                'date': date,
                'exit_date': exit_date,
                'spx_entry': spx_price,
                'spx_exit': exit_spx_price,
                'spx_change': spx_change,
                'long_strike': long_option['Strike'],
                'short_strike': short_option['Strike'],
                'long_entry_price': long_premium,
                'short_entry_price': short_premium,
                'net_debit': net_debit,
                'long_exit_price': exit_long_price,
                'short_exit_price': exit_short_price,
                'contracts': contracts,
                'long_pnl': long_pnl,
                'short_pnl': short_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0
            }

            trades.append(trade)
            successful_trades += 1

            # Show first few trades for debugging
            if successful_trades <= 5:
                print(f"✅ Trade {successful_trades}: SPX {spx_price:.0f}→{exit_spx_price:.0f} ({spx_change:+.0f}), "
                      f"Strikes {long_option['Strike']:.0f}/{short_option['Strike']:.0f}, "
                      f"Debit ${net_debit:.2f}, P&L ${net_pnl:+,.0f}")

        # Create results DataFrame
        if trades:
            trades_df = pd.DataFrame(trades)
            
            # Ensure trades directory exists
            os.makedirs('trades', exist_ok=True)
            
            # Save trades
            trades_df.to_csv('trades/simple_overnight_trades.csv', index=False)
            
            # Calculate performance metrics
            total_pnl = trades_df['net_pnl'].sum()
            win_rate = trades_df['win_loss_flag'].mean() * 100
            total_return = (total_pnl / (100000)) * 100  # Assuming $100k starting capital
            
            winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
            losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
            
            avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
            avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
            
            print(f"\n🎯 SIMPLE STRATEGY PERFORMANCE SUMMARY")
            print("=" * 50)
            print(f"📊 Total Trades: {len(trades_df)}")
            print(f"✅ Successful Setups: {successful_trades}")
            print(f"❌ Failed Setups: {failed_trades}")
            print(f"🎯 Win Rate: {win_rate:.1f}%")
            print(f"💰 Total P&L: ${total_pnl:+,.0f}")
            print(f"📈 Total Return: {total_return:+.1f}%")
            print(f"💪 Average Win: ${avg_win:+,.0f}")
            print(f"💔 Average Loss: ${avg_loss:+,.0f}")
            print(f"📦 Position Size: {contracts} contracts per trade")
            print(f"📊 Average SPX Change: {trades_df['spx_change'].mean():+.1f} points")
            print(f"📊 SPX Up Days: {(trades_df['spx_change'] > 0).sum()}/{len(trades_df)} ({(trades_df['spx_change'] > 0).mean()*100:.1f}%)")
            
            return trades_df
        else:
            print("❌ No successful trades executed")
            return None


if __name__ == "__main__":
    print("🎯 SIMPLE OVERNIGHT CALL SPREAD STRATEGY")
    print("=" * 50)
    
    # Execute strategy
    strategy = SimpleOvernightStrategy()
    results = strategy.execute_simple_strategy()
    
    if results is not None:
        print(f"\n✅ Simple strategy execution completed!")
        print(f"📊 Check trades/simple_overnight_trades.csv for detailed results")
    else:
        print("❌ Simple strategy execution failed")
