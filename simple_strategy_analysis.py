#!/usr/bin/env python3
"""
Simple Strategy Analysis
Analyze the simple overnight call spread results
"""

import pandas as pd
import numpy as np

def analyze_simple_strategy():
    """Analyze the simple overnight strategy results"""
    
    print("📊 SIMPLE OVERNIGHT STRATEGY ANALYSIS")
    print("=" * 50)
    
    # Load results
    try:
        trades_df = pd.read_csv('trades/simple_overnight_trades.csv')
        print(f"✅ Loaded {len(trades_df)} trades")
    except FileNotFoundError:
        print("❌ No trades file found. Run simple_overnight_strategy.py first.")
        return
    
    # Convert dates
    trades_df['date'] = pd.to_datetime(trades_df['date'])
    trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
    
    # Basic performance metrics
    total_pnl = trades_df['net_pnl'].sum()
    win_rate = trades_df['win_loss_flag'].mean() * 100
    total_return = (total_pnl / 100000) * 100  # Assuming $100k capital
    
    winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
    losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
    
    print(f"\n📈 PERFORMANCE SUMMARY:")
    print(f"   Total Trades: {len(trades_df)}")
    print(f"   Win Rate: {win_rate:.1f}%")
    print(f"   Total P&L: ${total_pnl:+,.0f}")
    print(f"   Total Return: {total_return:+.1f}%")
    print(f"   Winning Trades: {len(winning_trades)}")
    print(f"   Losing Trades: {len(losing_trades)}")
    
    if len(winning_trades) > 0:
        print(f"   Average Win: ${winning_trades['net_pnl'].mean():+,.0f}")
        print(f"   Largest Win: ${winning_trades['net_pnl'].max():+,.0f}")
    
    if len(losing_trades) > 0:
        print(f"   Average Loss: ${losing_trades['net_pnl'].mean():+,.0f}")
        print(f"   Largest Loss: ${losing_trades['net_pnl'].min():+,.0f}")
    
    # SPX movement analysis
    print(f"\n📊 SPX OVERNIGHT MOVEMENT ANALYSIS:")
    print(f"   Average SPX Change: {trades_df['spx_change'].mean():+.1f} points")
    print(f"   SPX Up Days: {(trades_df['spx_change'] > 0).sum()}/{len(trades_df)} ({(trades_df['spx_change'] > 0).mean()*100:.1f}%)")
    print(f"   SPX Down Days: {(trades_df['spx_change'] < 0).sum()}/{len(trades_df)} ({(trades_df['spx_change'] < 0).mean()*100:.1f}%)")
    print(f"   Largest Up Move: {trades_df['spx_change'].max():+.1f} points")
    print(f"   Largest Down Move: {trades_df['spx_change'].min():+.1f} points")
    
    # Strategy mechanics
    print(f"\n🎯 STRATEGY MECHANICS:")
    print(f"   Average Net Debit: ${trades_df['net_debit'].mean():.2f}")
    print(f"   Position Size: {trades_df['contracts'].iloc[0]} contracts per trade")
    print(f"   Spread Width: {trades_df['short_strike'].iloc[0] - trades_df['long_strike'].iloc[0]:.0f} points")
    print(f"   Average Long Strike Distance: {(trades_df['long_strike'] - trades_df['spx_entry']).mean():.1f} points OTM")
    
    # Performance by SPX movement
    print(f"\n📈 PERFORMANCE BY SPX MOVEMENT:")
    
    # Group by SPX movement direction
    up_days = trades_df[trades_df['spx_change'] > 0]
    down_days = trades_df[trades_df['spx_change'] <= 0]
    
    if len(up_days) > 0:
        up_pnl = up_days['net_pnl'].sum()
        up_win_rate = up_days['win_loss_flag'].mean() * 100
        print(f"   SPX Up Days ({len(up_days)} trades):")
        print(f"     Total P&L: ${up_pnl:+,.0f}")
        print(f"     Win Rate: {up_win_rate:.1f}%")
        print(f"     Avg P&L: ${up_days['net_pnl'].mean():+,.0f}")
    
    if len(down_days) > 0:
        down_pnl = down_days['net_pnl'].sum()
        down_win_rate = down_days['win_loss_flag'].mean() * 100
        print(f"   SPX Down Days ({len(down_days)} trades):")
        print(f"     Total P&L: ${down_pnl:+,.0f}")
        print(f"     Win Rate: {down_win_rate:.1f}%")
        print(f"     Avg P&L: ${down_days['net_pnl'].mean():+,.0f}")
    
    # Best and worst trades
    print(f"\n🏆 BEST TRADES:")
    best_trades = trades_df.nlargest(3, 'net_pnl')
    for i, (_, trade) in enumerate(best_trades.iterrows(), 1):
        print(f"   {i}. {trade['date'].strftime('%Y-%m-%d')}: SPX {trade['spx_change']:+.0f} pts, P&L ${trade['net_pnl']:+,.0f}")
    
    print(f"\n💔 WORST TRADES:")
    worst_trades = trades_df.nsmallest(3, 'net_pnl')
    for i, (_, trade) in enumerate(worst_trades.iterrows(), 1):
        print(f"   {i}. {trade['date'].strftime('%Y-%m-%d')}: SPX {trade['spx_change']:+.0f} pts, P&L ${trade['net_pnl']:+,.0f}")
    
    # Key insights
    print(f"\n💡 KEY INSIGHTS:")
    
    insights = []
    
    # Overnight bias
    up_pct = (trades_df['spx_change'] > 0).mean() * 100
    if up_pct > 55:
        insights.append(f"Strong overnight bias confirmed: {up_pct:.1f}% of days SPX goes up")
    
    # Win rate on up days
    if len(up_days) > 0:
        up_win_rate = up_days['win_loss_flag'].mean() * 100
        if up_win_rate > 70:
            insights.append(f"Strategy works well on up days: {up_win_rate:.1f}% win rate")
    
    # Profit factor
    total_wins = winning_trades['net_pnl'].sum() if len(winning_trades) > 0 else 0
    total_losses = abs(losing_trades['net_pnl'].sum()) if len(losing_trades) > 0 else 1
    profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
    if profit_factor > 1.5:
        insights.append(f"Good profit factor: {profit_factor:.2f} (wins/losses ratio)")
    
    # Position sizing
    avg_risk = trades_df['net_debit'].mean() * trades_df['contracts'].iloc[0] * 100
    if avg_risk < 25000:
        insights.append(f"Conservative position sizing: ${avg_risk:,.0f} average risk per trade")
    
    for i, insight in enumerate(insights, 1):
        print(f"   {i}. {insight}")
    
    # Recommendations
    print(f"\n🚀 RECOMMENDATIONS:")
    
    recommendations = []
    
    if win_rate > 50:
        recommendations.append("Strategy shows promise - consider live testing with small size")
    
    if len(up_days) > 0 and up_days['win_loss_flag'].mean() > 0.7:
        recommendations.append("Consider filtering for days with higher probability of SPX gains")
    
    if total_return > 20:
        recommendations.append("Strong returns - could scale up position size gradually")
    
    recommendations.append("Monitor overnight futures for additional confirmation signals")
    recommendations.append("Consider VIX levels as additional filter (avoid high VIX days)")
    
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")
    
    return trades_df

if __name__ == "__main__":
    analyze_simple_strategy()
