#!/usr/bin/env python3
"""
Detailed Drawdown Analysis for Call Spread Strategy
Identifies when and where the major drawdowns are occurring
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_drawdowns():
    """Comprehensive drawdown analysis"""
    
    print("🔍 DETAILED DRAWDOWN ANALYSIS")
    print("=" * 60)
    
    # Load trades data
    try:
        trades_df = pd.read_csv('trades/call_spread_trades.csv')
        print(f"✅ Loaded {len(trades_df)} trades")
    except FileNotFoundError:
        print("❌ No trades file found. Run call_spread_strategy.py first.")
        return
    
    # Convert dates
    trades_df['signal_date'] = pd.to_datetime(trades_df['signal_date'])
    trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
    trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
    
    # Sort by exit date for chronological analysis
    trades_df = trades_df.sort_values('exit_date')
    
    # Calculate cumulative performance
    starting_capital = 100000
    trades_df['cumulative_pnl'] = trades_df['net_pnl'].cumsum()
    trades_df['portfolio_value'] = starting_capital + trades_df['cumulative_pnl']
    trades_df['running_max'] = trades_df['portfolio_value'].expanding().max()
    trades_df['drawdown'] = trades_df['portfolio_value'] - trades_df['running_max']
    trades_df['drawdown_pct'] = (trades_df['drawdown'] / trades_df['running_max']) * 100
    
    # Identify major drawdown periods
    print("\n📉 MAJOR DRAWDOWN PERIODS")
    print("-" * 40)
    
    # Find drawdowns > 10%
    major_drawdowns = trades_df[trades_df['drawdown_pct'] < -10].copy()
    
    if len(major_drawdowns) > 0:
        print(f"Found {len(major_drawdowns)} trades with >10% drawdown")
        
        # Group consecutive drawdown periods
        major_drawdowns['period_group'] = (major_drawdowns['drawdown_pct'] > -10).cumsum()
        
        for period, group in major_drawdowns.groupby('period_group'):
            start_date = group['exit_date'].min()
            end_date = group['exit_date'].max()
            max_dd = group['drawdown_pct'].min()
            trades_in_period = len(group)
            
            print(f"\n🔴 Drawdown Period {period}:")
            print(f"   📅 Duration: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            print(f"   📉 Max Drawdown: {max_dd:.1f}%")
            print(f"   📊 Trades in period: {trades_in_period}")
            
            # Show worst trades in this period
            worst_trades = group.nsmallest(3, 'net_pnl')
            print(f"   💔 Worst trades:")
            for _, trade in worst_trades.iterrows():
                print(f"      {trade['exit_date'].strftime('%Y-%m-%d')}: ${trade['net_pnl']:,.0f} "
                      f"(SPX: {trade['spx_price']:.0f}, Confidence: {trade['confidence_score']:.2f})")
    
    # Analyze by time periods
    print("\n📊 PERFORMANCE BY TIME PERIOD")
    print("-" * 40)
    
    trades_df['year'] = trades_df['exit_date'].dt.year
    trades_df['quarter'] = trades_df['exit_date'].dt.quarter
    trades_df['month'] = trades_df['exit_date'].dt.month
    
    # Yearly analysis
    yearly_stats = trades_df.groupby('year').agg({
        'net_pnl': ['sum', 'count', 'mean'],
        'win_loss_flag': 'mean',
        'drawdown_pct': 'min'
    }).round(2)
    
    print("\n📅 YEARLY PERFORMANCE:")
    for year in yearly_stats.index:
        total_pnl = yearly_stats.loc[year, ('net_pnl', 'sum')]
        trade_count = yearly_stats.loc[year, ('net_pnl', 'count')]
        win_rate = yearly_stats.loc[year, ('win_loss_flag', 'mean')] * 100
        max_dd = yearly_stats.loc[year, ('drawdown_pct', 'min')]
        
        print(f"   {year}: ${total_pnl:,.0f} ({trade_count} trades, {win_rate:.1f}% win rate, {max_dd:.1f}% max DD)")
    
    # Monthly analysis for worst months
    monthly_pnl = trades_df.groupby([trades_df['exit_date'].dt.to_period('M')])['net_pnl'].sum()
    worst_months = monthly_pnl.nsmallest(5)
    
    print("\n📉 WORST PERFORMING MONTHS:")
    for month, pnl in worst_months.items():
        month_trades = trades_df[trades_df['exit_date'].dt.to_period('M') == month]
        win_rate = month_trades['win_loss_flag'].mean() * 100
        print(f"   {month}: ${pnl:,.0f} ({len(month_trades)} trades, {win_rate:.1f}% win rate)")
    
    # Analyze by market conditions
    print("\n📊 PERFORMANCE BY MARKET CONDITIONS")
    print("-" * 40)
    
    # VIX levels
    trades_df['vix_level'] = pd.cut(trades_df['spx_price'], 
                                   bins=[0, 4500, 5000, 5500, 6000, 10000],
                                   labels=['<4500', '4500-5000', '5000-5500', '5500-6000', '>6000'])
    
    vix_performance = trades_df.groupby('vix_level').agg({
        'net_pnl': ['sum', 'count', 'mean'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    print("\n📈 PERFORMANCE BY SPX LEVEL:")
    for level in vix_performance.index:
        if pd.isna(level):
            continue
        total_pnl = vix_performance.loc[level, ('net_pnl', 'sum')]
        trade_count = vix_performance.loc[level, ('net_pnl', 'count')]
        avg_pnl = vix_performance.loc[level, ('net_pnl', 'mean')]
        win_rate = vix_performance.loc[level, ('win_loss_flag', 'mean')] * 100
        
        print(f"   SPX {level}: ${total_pnl:,.0f} ({trade_count} trades, ${avg_pnl:,.0f} avg, {win_rate:.1f}% win rate)")
    
    # Confidence score analysis
    trades_df['confidence_bucket'] = pd.cut(trades_df['confidence_score'], 
                                          bins=[0, 0.7, 0.8, 0.9, 1.0],
                                          labels=['<0.7', '0.7-0.8', '0.8-0.9', '0.9+'])
    
    confidence_performance = trades_df.groupby('confidence_bucket').agg({
        'net_pnl': ['sum', 'count', 'mean'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    print("\n🎯 PERFORMANCE BY CONFIDENCE SCORE:")
    for conf in confidence_performance.index:
        if pd.isna(conf):
            continue
        total_pnl = confidence_performance.loc[conf, ('net_pnl', 'sum')]
        trade_count = confidence_performance.loc[conf, ('net_pnl', 'count')]
        avg_pnl = confidence_performance.loc[conf, ('net_pnl', 'mean')]
        win_rate = confidence_performance.loc[conf, ('win_loss_flag', 'mean')] * 100
        
        print(f"   Confidence {conf}: ${total_pnl:,.0f} ({trade_count} trades, ${avg_pnl:,.0f} avg, {win_rate:.1f}% win rate)")
    
    # Create visualization
    create_drawdown_charts(trades_df)
    
    # Summary recommendations
    print("\n🎯 DRAWDOWN ANALYSIS SUMMARY")
    print("=" * 60)
    
    max_drawdown = trades_df['drawdown_pct'].min()
    worst_trade = trades_df.loc[trades_df['net_pnl'].idxmin()]
    
    print(f"📉 Maximum Drawdown: {max_drawdown:.1f}%")
    print(f"💔 Worst Single Trade: ${worst_trade['net_pnl']:,.0f} on {worst_trade['exit_date'].strftime('%Y-%m-%d')}")
    print(f"📊 Win Rate: {trades_df['win_loss_flag'].mean() * 100:.1f}%")
    print(f"💰 Total P&L: ${trades_df['net_pnl'].sum():,.0f}")
    
    # Identify patterns
    losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
    if len(losing_trades) > 0:
        print(f"\n🔍 LOSING TRADE PATTERNS:")
        print(f"   📊 {len(losing_trades)} losing trades out of {len(trades_df)} total")
        print(f"   💔 Average loss: ${losing_trades['net_pnl'].mean():,.0f}")
        print(f"   📈 Average SPX level on losses: {losing_trades['spx_price'].mean():.0f}")
        print(f"   🎯 Average confidence on losses: {losing_trades['confidence_score'].mean():.2f}")
    
    return trades_df

def create_drawdown_charts(trades_df):
    """Create visualization charts for drawdown analysis"""
    
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Call Spread Strategy - Drawdown Analysis', fontsize=16, fontweight='bold')
    
    # 1. Cumulative P&L and Drawdown
    ax1 = axes[0, 0]
    ax1.plot(trades_df['exit_date'], trades_df['cumulative_pnl'], 'b-', linewidth=2, label='Cumulative P&L')
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.set_title('Cumulative P&L Over Time')
    ax1.set_ylabel('Cumulative P&L ($)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. Drawdown percentage
    ax2 = axes[0, 1]
    ax2.fill_between(trades_df['exit_date'], 0, trades_df['drawdown_pct'], 
                     color='red', alpha=0.7, label='Drawdown %')
    ax2.set_title('Drawdown Percentage Over Time')
    ax2.set_ylabel('Drawdown (%)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 3. Monthly P&L
    ax3 = axes[1, 0]
    monthly_pnl = trades_df.groupby(trades_df['exit_date'].dt.to_period('M'))['net_pnl'].sum()
    colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
    ax3.bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors, alpha=0.7)
    ax3.set_title('Monthly P&L Distribution')
    ax3.set_ylabel('Monthly P&L ($)')
    ax3.set_xticks(range(0, len(monthly_pnl), max(1, len(monthly_pnl)//6)))
    ax3.set_xticklabels([str(monthly_pnl.index[i]) for i in range(0, len(monthly_pnl), max(1, len(monthly_pnl)//6))], rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # 4. Trade P&L distribution
    ax4 = axes[1, 1]
    ax4.hist(trades_df['net_pnl'], bins=20, alpha=0.7, color='blue', edgecolor='black')
    ax4.axvline(x=0, color='red', linestyle='--', linewidth=2, label='Break-even')
    ax4.set_title('Trade P&L Distribution')
    ax4.set_xlabel('Trade P&L ($)')
    ax4.set_ylabel('Frequency')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('charts/drawdown_analysis.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 Drawdown analysis chart saved to: charts/drawdown_analysis.png")
    plt.show()

if __name__ == "__main__":
    analyze_drawdowns()
