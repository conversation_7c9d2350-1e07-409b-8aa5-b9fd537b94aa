#!/bin/bash

# JPM Options Trading Strategies - Automated Execution Script
# Loads environment variables and runs both call spread and single options strategies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}============================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}📊 $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to load environment variables
load_environment() {
    print_header "LOADING ENVIRONMENT VARIABLES"
    
    # Check if .env file exists
    if [ -f ".env" ]; then
        print_success "Found .env file"
        
        # Load environment variables
        set -a  # Automatically export all variables
        source .env
        set +a  # Stop automatically exporting
        
        print_success "Environment variables loaded from .env"
        
        # Check for OpenAI API key
        if [ -n "$OPENAI_API_KEY" ]; then
            print_success "OpenAI API key found (for ChatGPT narratives)"
        else
            print_warning "OpenAI API key not found - will use default narratives"
        fi
        
    else
        print_warning ".env file not found - creating template"
        
        # Create template .env file
        cat > .env << 'EOF'
# JPM Options Trading Strategies Environment Variables

# OpenAI API Key (optional - for enhanced PDF narratives)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Strategy Configuration
STRATEGY_START_DATE=2023-05-01
STRATEGY_END_DATE=auto
STARTING_CAPITAL=100000

# Risk Management
MAX_POSITION_SIZE=60
MIN_POSITION_SIZE=5
MAX_DRAWDOWN_THRESHOLD=2.0

# Data Paths (adjust if needed)
VIX_DATA_PATH=/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/VIX.csv
OPTIONS_DATA_PATH=../optionshistory

# Reporting
GENERATE_PDF_REPORTS=true
INCLUDE_CHARTS=true
INCLUDE_NARRATIVES=true
EOF
        
        print_info "Template .env file created - please edit with your settings"
        print_info "You can run the script again after updating the .env file"
    fi
}

# Function to activate virtual environment
activate_virtual_environment() {
    print_header "CHECKING VIRTUAL ENVIRONMENT"

    # Check for virtual environment
    if [ -d ".venv" ]; then
        print_success "Found .venv directory"

        # Activate virtual environment
        if [ -f ".venv/bin/activate" ]; then
            print_info "Activating Python virtual environment..."
            source .venv/bin/activate
            print_success "Virtual environment activated"
        elif [ -f ".venv/Scripts/activate" ]; then
            # Windows path
            print_info "Activating Python virtual environment (Windows)..."
            source .venv/Scripts/activate
            print_success "Virtual environment activated"
        else
            print_warning "Virtual environment found but activation script missing"
        fi
    elif [ -d "venv" ]; then
        print_success "Found venv directory"

        # Activate virtual environment
        if [ -f "venv/bin/activate" ]; then
            print_info "Activating Python virtual environment..."
            source venv/bin/activate
            print_success "Virtual environment activated"
        elif [ -f "venv/Scripts/activate" ]; then
            # Windows path
            print_info "Activating Python virtual environment (Windows)..."
            source venv/Scripts/activate
            print_success "Virtual environment activated"
        else
            print_warning "Virtual environment found but activation script missing"
        fi
    else
        print_warning "No virtual environment found (.venv or venv)"

        # Ask user if they want to create one
        read -p "Would you like to create a virtual environment? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "Creating virtual environment..."
            if python3 -m venv .venv; then
                print_success "Virtual environment created in .venv"

                # Activate it
                if [ -f ".venv/bin/activate" ]; then
                    source .venv/bin/activate
                    print_success "Virtual environment activated"
                elif [ -f ".venv/Scripts/activate" ]; then
                    source .venv/Scripts/activate
                    print_success "Virtual environment activated"
                fi
            else
                print_error "Failed to create virtual environment"
            fi
        else
            print_info "Continuing without virtual environment"
        fi
    fi
}

# Function to check Python environment
check_python_environment() {
    print_header "CHECKING PYTHON ENVIRONMENT"

    # Check Python version
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version 2>&1)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3 not found - please install Python 3.7+"
        exit 1
    fi

    # Check if we're in a virtual environment
    if [ -n "$VIRTUAL_ENV" ]; then
        print_success "Running in virtual environment: $VIRTUAL_ENV"
    else
        print_warning "Not running in a virtual environment"
    fi
    
    # Check required packages
    print_info "Checking required Python packages..."

    REQUIRED_PACKAGES=(
        "pandas"
        "numpy"
        "matplotlib"
        "seaborn"
        "reportlab"
        "openai"
        "pandas_market_calendars"
    )
    
    MISSING_PACKAGES=()
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            print_success "$package installed"
        else
            MISSING_PACKAGES+=("$package")
            print_warning "$package not found"
        fi
    done
    
    # Use pip or pip3 depending on what's available and if we're in venv
    PIP_CMD="pip3"
    if [ -n "$VIRTUAL_ENV" ] && command_exists pip; then
        PIP_CMD="pip"
    fi

    # Check if requirements.txt exists and install from it
    if [ -f "requirements.txt" ] && [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
        print_info "Found requirements.txt - installing all dependencies..."

        # Upgrade pip first
        print_info "Upgrading pip..."
        $PIP_CMD install --upgrade pip

        # Install from requirements.txt
        if $PIP_CMD install -r requirements.txt; then
            print_success "All packages installed from requirements.txt"
        else
            print_warning "Failed to install from requirements.txt - trying individual packages"

            # Fall back to individual package installation
            if $PIP_CMD install "${MISSING_PACKAGES[@]}"; then
                print_success "Missing packages installed individually"
            else
                print_error "Failed to install packages - please install manually"
                print_info "Try: $PIP_CMD install -r requirements.txt"
                exit 1
            fi
        fi
    elif [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
        print_info "Installing missing packages: ${MISSING_PACKAGES[*]}"

        # Upgrade pip first
        print_info "Upgrading pip..."
        $PIP_CMD install --upgrade pip

        # Install packages
        if $PIP_CMD install "${MISSING_PACKAGES[@]}"; then
            print_success "All packages installed successfully"
        else
            print_error "Failed to install packages - please install manually"
            print_info "Try: $PIP_CMD install ${MISSING_PACKAGES[*]}"
            exit 1
        fi
    else
        print_success "All required packages are installed"
    fi
}

# Function to run call spread strategy
run_call_spread_strategy() {
    print_header "EXECUTING CALL SPREAD STRATEGY"
    
    if [ -f "call_spread_strategy.py" ]; then
        print_info "Running two-leg call spread strategy..."
        
        # Run the strategy
        if python3 call_spread_strategy.py; then
            print_success "Call spread strategy executed successfully"
            
            # Check if trades were generated
            if [ -f "trades/call_spread_trades.csv" ]; then
                TRADE_COUNT=$(tail -n +2 trades/call_spread_trades.csv | wc -l)
                print_success "Generated $TRADE_COUNT call spread trades"
            fi
        else
            print_error "Call spread strategy execution failed"
            return 1
        fi
    else
        print_error "call_spread_strategy.py not found"
        return 1
    fi
}

# Function to check data availability
check_data_availability() {
    print_header "CHECKING DATA AVAILABILITY"

    # Check for options data directory
    if [ -d "../optionshistory" ]; then
        print_success "Options data directory found: ../optionshistory"

        # Count available data files
        DATA_FILES=$(find ../optionshistory -name "spx_complete_*.csv" 2>/dev/null | wc -l)
        if [ "$DATA_FILES" -gt 0 ]; then
            print_success "Found $DATA_FILES SPX options data files"
        else
            print_warning "No SPX options data files found in ../optionshistory"
        fi
    else
        print_warning "Options data directory not found: ../optionshistory"
        print_info "Strategy will run with limited data validation"
    fi

    # Check for VIX data (if needed)
    VIX_PATH="${VIX_DATA_PATH:-/Users/<USER>/Downloads/CurrentSystems/strategy_package/data/securities/VIX.csv}"
    if [ -f "$VIX_PATH" ]; then
        print_success "VIX data file found: $VIX_PATH"
    else
        print_warning "VIX data file not found: $VIX_PATH"
        print_info "Strategy will use default VIX values"
    fi
}

# Function to generate PDF reports
generate_pdf_reports() {
    print_header "GENERATING PDF REPORTS"
    
    # Generate call spread PDF
    if [ -f "call_spread_pdf_generator.py" ] && [ -f "trades/call_spread_trades.csv" ]; then
        print_info "Generating call spread PDF report..."
        
        if python3 call_spread_pdf_generator.py; then
            print_success "Call spread PDF report generated"
        else
            print_warning "Call spread PDF generation failed"
        fi
    fi
    
    # Check for any additional PDF generators
    for pdf_generator in *_pdf_generator.py; do
        if [ -f "$pdf_generator" ] && [ "$pdf_generator" != "call_spread_pdf_generator.py" ]; then
            print_info "Found additional PDF generator: $pdf_generator"

            if python3 "$pdf_generator"; then
                print_success "Additional PDF report generated using $pdf_generator"
            else
                print_warning "Failed to generate PDF with $pdf_generator"
            fi
        fi
    done
}

# Function to display summary
display_summary() {
    print_header "EXECUTION SUMMARY"

    # Call spread results
    if [ -f "trades/call_spread_trades.csv" ]; then
        print_info "Call Spread Strategy Results:"
        python3 -c "
import pandas as pd
try:
    trades_df = pd.read_csv('trades/call_spread_trades.csv')
    if len(trades_df) > 0:
        total_return = (trades_df['net_pnl'].sum() / 100000) * 100
        win_rate = trades_df['win_loss_flag'].mean() * 100
        max_drawdown = 0  # Calculate if needed
        profit_factor = trades_df[trades_df['net_pnl'] > 0]['net_pnl'].sum() / abs(trades_df[trades_df['net_pnl'] < 0]['net_pnl'].sum()) if trades_df[trades_df['net_pnl'] < 0]['net_pnl'].sum() != 0 else float('inf')

        print(f'  📈 Total Return: {total_return:.1f}%')
        print(f'  🎯 Win Rate: {win_rate:.1f}%')
        print(f'  📊 Total Trades: {len(trades_df)}')
        print(f'  💰 Total P&L: \${trades_df[\"net_pnl\"].sum():,.0f}')
        print(f'  ⚖️ Profit Factor: {profit_factor:.2f}')
        print(f'  📊 Avg Spread Width: {trades_df[\"spread_width\"].mean():.0f} points')
        print(f'  💰 Avg Net Credit: \${trades_df[\"net_credit\"].mean():.2f}')
    else:
        print('  ❌ No trades found in call spread data')
except Exception as e:
    print(f'  ❌ Error reading call spread data: {e}')
"
    else
        print_warning "Call spread trades file not found"
    fi

    # PDF reports
    print_info "Generated Reports:"
    if ls reports/*.pdf 1> /dev/null 2>&1; then
        for pdf in reports/*.pdf; do
            if [ -f "$pdf" ]; then
                FILENAME=$(basename "$pdf")
                FILESIZE=$(du -h "$pdf" | cut -f1)
                CREATED=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$pdf" 2>/dev/null || stat -c "%y" "$pdf" 2>/dev/null | cut -d' ' -f1-2)
                print_success "  📄 $FILENAME ($FILESIZE) - Created: $CREATED"
            fi
        done
    else
        print_warning "  No PDF reports found"
    fi

    # Charts
    print_info "Generated Charts:"
    if ls reports/*.png 1> /dev/null 2>&1; then
        for chart in reports/*.png; do
            if [ -f "$chart" ]; then
                FILENAME=$(basename "$chart")
                print_success "  📊 $FILENAME"
            fi
        done
    else
        print_warning "  No chart files found"
    fi
}

# Main execution function
main() {
    print_header "JPM CALL SPREAD STRATEGY - AUTOMATED EXECUTION"
    echo -e "${PURPLE}🚀 Two-Leg Call Spread Options Strategy${NC}"
    echo -e "${PURPLE}📊 Real SPX Market Data with Proper Strike Selection${NC}"
    echo -e "${PURPLE}🎯 80% Trade Validation Success Rate${NC}"
    echo ""
    
    # Create necessary directories
    mkdir -p trades reports backups
    
    # Load environment
    load_environment

    # Activate virtual environment
    activate_virtual_environment

    # Check Python environment
    check_python_environment

    # Check data availability
    check_data_availability

    # Run strategies
    print_header "EXECUTING TRADING STRATEGIES"

    # Run call spread strategy
    if run_call_spread_strategy; then
        print_success "Call spread strategy completed"
    else
        print_error "Call spread strategy failed"
    fi
    
    echo ""
    
    # Generate PDF reports
    if [ "${GENERATE_PDF_REPORTS:-true}" = "true" ]; then
        generate_pdf_reports
    else
        print_info "PDF generation skipped (GENERATE_PDF_REPORTS=false)"
    fi
    
    echo ""
    
    # Display summary
    display_summary
    
    print_header "EXECUTION COMPLETED SUCCESSFULLY"
    print_success "Call spread strategy executed and reports generated"
    print_info "Check the 'reports/' directory for PDF files"
    print_info "Check the 'trades/' directory for CSV data"

    # Check if we have a summary file
    if [ -f "CALL_SPREAD_STRATEGY_SUMMARY.md" ]; then
        print_info "For detailed strategy information, see CALL_SPREAD_STRATEGY_SUMMARY.md"
    fi
}

# Run main function
main "$@"
