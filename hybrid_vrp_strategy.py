#!/usr/bin/env python3
"""
Hybrid VRP Strategy
Combines simple overnight strategy with VRP-based position sizing
- Trade every valid day (like simple strategy)
- Use VRP to determine position size (more negative VRP = larger position)
- Keep the successful simple mechanics
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta

class HybridVRPStrategy:
    """
    Hybrid strategy combining simple overnight approach with VRP intelligence
    """
    
    def __init__(self):
        self.spx_options_data = None
        self.vrp_data = None
        self.spread_width = 150
        self.otm_offset = 25
        
    def load_spx_options_data(self):
        """Load SPX options data"""
        print("📊 Loading SPX options data...")
        
        options_dir = "/Users/<USER>/Downloads/optionhistory"
        if not os.path.exists(options_dir):
            return None

        all_files = []
        for year in range(2023, 2026):
            for quarter in range(1, 5):
                pattern = f"{options_dir}/{year}_q{quarter}_option_chain/spx_complete_{year}_q{quarter}.csv"
                files = glob.glob(pattern)
                all_files.extend(files)

        if not all_files:
            return None

        all_data = []
        for file in all_files:
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"   ✅ Loaded {len(df)} records from {os.path.basename(file)}")
            except Exception as e:
                print(f"   ❌ Error loading {file}: {e}")

        if not all_data:
            return None

        combined_data = pd.concat(all_data, ignore_index=True)
        combined_data['date'] = pd.to_datetime(combined_data['date'])
        combined_data['expiry_date'] = pd.to_datetime(combined_data['Expiry Date'])
        combined_data['option_price'] = combined_data['Last Trade Price']
        
        combined_data = combined_data.dropna(subset=['option_price', 'Strike', 'spx_close'])
        combined_data = combined_data[combined_data['option_price'] > 0]
        
        print(f"✅ Combined SPX options data: {len(combined_data)} records")
        return combined_data

    def calculate_simple_vrp(self):
        """Calculate simplified VRP for position sizing"""
        print("📊 Calculating simplified VRP...")
        
        # Get daily SPX data
        daily_data = self.spx_options_data.groupby('date').agg({
            'spx_close': 'first'
        }).reset_index().sort_values('date')
        
        # Calculate 10-day realized volatility (shorter window for responsiveness)
        daily_data['spx_returns'] = daily_data['spx_close'].pct_change()
        daily_data['realized_vol_10d'] = daily_data['spx_returns'].rolling(10).std() * np.sqrt(252) * 100
        
        # Calculate simple implied volatility proxy from ATM options
        vrp_data = []
        
        for date in daily_data['date']:
            spx_price = daily_data[daily_data['date'] == date]['spx_close'].iloc[0]
            realized_vol = daily_data[daily_data['date'] == date]['realized_vol_10d'].iloc[0]
            
            if pd.isna(realized_vol):
                continue
                
            # Get ATM options
            day_options = self.spx_options_data[
                (self.spx_options_data['date'] == date) &
                (self.spx_options_data['option_price'] > 0)
            ].copy()
            
            if len(day_options) == 0:
                continue
                
            day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days
            day_options = day_options[(day_options['days_to_expiry'] >= 15) & (day_options['days_to_expiry'] <= 45)]
            
            if len(day_options) == 0:
                continue
                
            # Find ATM options
            day_options['strike_distance'] = abs(day_options['Strike'] - spx_price)
            atm_options = day_options[day_options['strike_distance'] <= 75]
            
            if len(atm_options) == 0:
                continue
                
            # Simple IV calculation
            atm_options = atm_options.copy()
            atm_options['iv_proxy'] = (atm_options['option_price'] / spx_price) * np.sqrt(365 / atm_options['days_to_expiry']) * 100
            
            implied_vol = atm_options['iv_proxy'].median()
            
            if pd.isna(implied_vol) or implied_vol <= 0:
                continue
                
            vrp = implied_vol - realized_vol
            
            vrp_data.append({
                'date': date,
                'spx_close': spx_price,
                'implied_vol': implied_vol,
                'realized_vol': realized_vol,
                'vrp': vrp
            })
        
        vrp_df = pd.DataFrame(vrp_data)
        print(f"✅ Calculated VRP for {len(vrp_df)} dates")
        return vrp_df

    def get_vrp_position_size(self, vrp):
        """
        Determine position size based on VRP
        More negative VRP = options are cheaper = larger position
        """
        if vrp < -15:
            return 8  # Very cheap options
        elif vrp < -10:
            return 6  # Cheap options
        elif vrp < -5:
            return 5  # Moderately cheap options
        elif vrp < 0:
            return 4  # Slightly cheap options
        else:
            return 3  # Expensive options (rare)

    def find_call_spread_options(self, date, spx_price):
        """Find call spread options (same as simple strategy)"""
        
        day_options = self.spx_options_data[
            (self.spx_options_data['date'] == date) &
            (self.spx_options_data['Call/Put'] == 'c')
        ].copy()

        if len(day_options) == 0:
            return None, None

        day_options['expiry_date'] = pd.to_datetime(day_options['expiry_date'])
        day_options['days_to_expiry'] = (day_options['expiry_date'] - day_options['date']).dt.days
        day_options = day_options[day_options['days_to_expiry'] >= 20].copy()
        
        if len(day_options) == 0:
            return None, None

        # Round SPX to nearest 25
        quotient = spx_price / 25
        remainder = quotient - int(quotient)
        spx_rounded = (int(quotient) + 1) * 25 if remainder > 0.5 else int(quotient) * 25

        # Strike selection
        long_strike = spx_rounded + self.otm_offset
        short_strike = long_strike + self.spread_width

        # Find options
        long_option = day_options.iloc[(day_options['Strike'] - long_strike).abs().argsort()[:1]]
        short_option = day_options.iloc[(day_options['Strike'] - short_strike).abs().argsort()[:1]]

        if len(long_option) == 0 or len(short_option) == 0:
            return None, None

        long_option = long_option.iloc[0]
        short_option = short_option.iloc[0]

        if long_option['option_price'] <= 0 or short_option['option_price'] <= 0:
            return None, None

        return long_option, short_option

    def get_next_trading_day(self, date):
        """Get next trading day"""
        next_day = date + timedelta(days=1)
        while next_day.weekday() >= 5:
            next_day += timedelta(days=1)
        return next_day

    def execute_hybrid_strategy(self):
        """Execute hybrid VRP strategy"""
        print("🚀 EXECUTING HYBRID VRP STRATEGY")
        print("=" * 50)
        print("📋 Simple overnight mechanics + VRP position sizing")
        print("=" * 50)

        # Load data
        self.spx_options_data = self.load_spx_options_data()
        if self.spx_options_data is None:
            return None

        self.vrp_data = self.calculate_simple_vrp()
        if self.vrp_data is None or len(self.vrp_data) == 0:
            return None

        trades = []
        successful_trades = 0
        failed_trades = 0

        # Test on every 3rd day (like simple strategy)
        unique_dates = sorted(self.spx_options_data['date'].unique())
        test_dates = unique_dates[::3]
        
        # Filter for dates with VRP data
        vrp_dates = set(self.vrp_data['date'])
        test_dates = [d for d in test_dates if d in vrp_dates]
        
        print(f"📊 Testing on {len(test_dates)} dates")

        for date in test_dates:
            # Get VRP for position sizing
            vrp_row = self.vrp_data[self.vrp_data['date'] == date]
            if len(vrp_row) == 0:
                failed_trades += 1
                continue
                
            vrp = vrp_row['vrp'].iloc[0]
            spx_price = vrp_row['spx_close'].iloc[0]

            # Find options
            long_option, short_option = self.find_call_spread_options(date, spx_price)
            if long_option is None or short_option is None:
                failed_trades += 1
                continue

            # Calculate spread
            long_premium = long_option['option_price']
            short_premium = short_option['option_price']
            net_debit = long_premium - short_premium

            # Simple filters (like original strategy)
            if net_debit <= 5.0 or net_debit >= 50.0:
                failed_trades += 1
                continue

            # VRP-BASED POSITION SIZING
            contracts = self.get_vrp_position_size(vrp)

            # Get exit prices
            exit_date = self.get_next_trading_day(date)

            exit_long_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == long_option['Strike']) &
                (self.spx_options_data['expiry_date'] == long_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            exit_short_options = self.spx_options_data[
                (self.spx_options_data['date'] == exit_date) &
                (self.spx_options_data['Strike'] == short_option['Strike']) &
                (self.spx_options_data['expiry_date'] == short_option['expiry_date']) &
                (self.spx_options_data['Call/Put'] == 'c')
            ]

            if len(exit_long_options) == 0 or len(exit_short_options) == 0:
                failed_trades += 1
                continue

            exit_long_price = exit_long_options['option_price'].iloc[0]
            exit_short_price = exit_short_options['option_price'].iloc[0]

            # Calculate P&L
            long_pnl = (exit_long_price - long_premium) * contracts * 100
            short_pnl = (short_premium - exit_short_price) * contracts * 100
            total_pnl = long_pnl + short_pnl

            commission = contracts * 2 * 2
            net_pnl = total_pnl - commission

            exit_spx_price = exit_long_options['spx_close'].iloc[0]
            spx_change = exit_spx_price - spx_price

            # Create trade record
            trade = {
                'date': date,
                'exit_date': exit_date,
                'spx_entry': spx_price,
                'spx_exit': exit_spx_price,
                'spx_change': spx_change,
                'vrp': vrp,
                'long_strike': long_option['Strike'],
                'short_strike': short_option['Strike'],
                'long_entry_price': long_premium,
                'short_entry_price': short_premium,
                'net_debit': net_debit,
                'long_exit_price': exit_long_price,
                'short_exit_price': exit_short_price,
                'contracts': contracts,
                'long_pnl': long_pnl,
                'short_pnl': short_pnl,
                'total_pnl': total_pnl,
                'commission': commission,
                'net_pnl': net_pnl,
                'win_loss_flag': 1 if net_pnl > 0 else 0
            }

            trades.append(trade)
            successful_trades += 1

            # Show first few trades
            if successful_trades <= 5:
                print(f"✅ Trade {successful_trades}: VRP {vrp:.2f}, {contracts} contracts, "
                      f"SPX {spx_price:.0f}→{exit_spx_price:.0f} ({spx_change:+.0f}), P&L ${net_pnl:+,.0f}")

        # Results
        if trades:
            trades_df = pd.DataFrame(trades)
            
            os.makedirs('trades', exist_ok=True)
            trades_df.to_csv('trades/hybrid_vrp_trades.csv', index=False)
            
            total_pnl = trades_df['net_pnl'].sum()
            win_rate = trades_df['win_loss_flag'].mean() * 100
            total_return = (total_pnl / 100000) * 100
            
            winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
            losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
            
            avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
            avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
            
            print(f"\n🎯 HYBRID VRP STRATEGY PERFORMANCE")
            print("=" * 45)
            print(f"📊 Total Trades: {len(trades_df)}")
            print(f"🎯 Win Rate: {win_rate:.1f}%")
            print(f"💰 Total P&L: ${total_pnl:+,.0f}")
            print(f"📈 Total Return: {total_return:+.1f}%")
            print(f"💪 Average Win: ${avg_win:+,.0f}")
            print(f"💔 Average Loss: ${avg_loss:+,.0f}")
            print(f"📦 Average Position: {trades_df['contracts'].mean():.1f} contracts")
            print(f"📊 Average VRP: {trades_df['vrp'].mean():.2f}")
            print(f"📊 SPX Up Days: {(trades_df['spx_change'] > 0).sum()}/{len(trades_df)} ({(trades_df['spx_change'] > 0).mean()*100:.1f}%)")
            
            return trades_df
        else:
            print("❌ No successful trades")
            return None


if __name__ == "__main__":
    strategy = HybridVRPStrategy()
    results = strategy.execute_hybrid_strategy()
    
    if results is not None:
        print(f"\n✅ Hybrid strategy completed!")
        print(f"📊 Check trades/hybrid_vrp_trades.csv for results")
