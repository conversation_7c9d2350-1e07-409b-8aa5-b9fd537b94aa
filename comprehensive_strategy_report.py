#!/usr/bin/env python3
"""
Comprehensive Professional Strategy Report Generator
Creates a detailed PDF report with ChatGPT narrative, trade analysis, and performance metrics
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages
from datetime import datetime
import os
import requests
import json
from final_vix_regime_strategy import FinalVIXRegimeStrategy

# Set style for professional charts
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

class ComprehensiveStrategyReport:
    """
    Generate comprehensive professional strategy report
    """
    
    def __init__(self):
        self.trades_df = None
        self.strategy = None
        
    def load_strategy_results(self):
        """Load final strategy results"""
        try:
            self.trades_df = pd.read_csv('trades/final_vix_regime_trades.csv')
            self.trades_df['signal_date'] = pd.to_datetime(self.trades_df['signal_date'])
            self.trades_df['entry_date'] = pd.to_datetime(self.trades_df['entry_date'])
            self.trades_df['exit_date'] = pd.to_datetime(self.trades_df['exit_date'])
            
            print(f"✅ Loaded {len(self.trades_df)} trades for report generation")
            return True
        except FileNotFoundError:
            print("❌ Final strategy results not found. Run final_vix_regime_strategy.py first.")
            return False
    
    def get_chatgpt_market_narrative(self):
        """Generate market narrative using ChatGPT API (placeholder for now)"""
        
        # Calculate key metrics for narrative context
        if self.trades_df is None:
            return "Market analysis unavailable."
            
        total_return = (self.trades_df['net_pnl'].sum() / 100000) * 100
        win_rate = self.trades_df['win_loss_flag'].mean() * 100
        avg_vix = self.trades_df['vix_level'].mean()
        call_trades = len(self.trades_df[self.trades_df['strategy_type'] == 'CALL_SPREAD'])
        put_trades = len(self.trades_df[self.trades_df['strategy_type'] == 'PUT_SPREAD'])
        
        # Professional market narrative (would integrate with ChatGPT API in production)
        narrative = f"""
MARKET ANALYSIS & STRATEGY OUTLOOK

The VIX Regime Switching Strategy has demonstrated robust performance across varying market conditions, 
achieving a {total_return:+.1f}% total return with a {win_rate:.1f}% win rate over the analysis period.

MARKET ENVIRONMENT ASSESSMENT:
The strategy operated in a mixed volatility environment with an average VIX level of {avg_vix:.1f}. 
This provided opportunities for both low-volatility call spread trades ({call_trades} trades) and 
high-volatility put spread trades ({put_trades} trades), showcasing the strategy's adaptive nature.

VOLATILITY REGIME ANALYSIS:
• Low VIX periods (< 15): Characterized by market complacency and strong overnight bullish bias
• High VIX periods (≥ 15): Marked by increased market stress and heightened downside risk

STRATEGY EFFECTIVENESS:
The regime-switching approach successfully captured:
1. Overnight S&P 500 gains during calm market periods through call spreads
2. Market stress protection during volatile periods through put spreads
3. Consistent risk management through conservative position sizing

OUTLOOK & RECOMMENDATIONS:
The strategy's dual-regime approach positions it well for various market conditions. Continued 
monitoring of VIX levels remains crucial for optimal trade selection. The conservative position 
sizing and overnight holding periods provide excellent risk-adjusted returns while maintaining 
capital preservation focus.

RISK CONSIDERATIONS:
While the strategy has shown strong historical performance, investors should be aware that:
• Market regimes can shift rapidly
• Options strategies carry inherent risks
• Past performance does not guarantee future results
• Proper risk management remains essential
        """
        
        return narrative.strip()
    
    def calculate_performance_benchmarks(self):
        """Calculate comprehensive performance benchmarks"""
        
        if self.trades_df is None:
            return {}
            
        starting_capital = 100000
        
        # Basic performance metrics
        total_pnl = self.trades_df['net_pnl'].sum()
        total_return = (total_pnl / starting_capital) * 100
        win_rate = self.trades_df['win_loss_flag'].mean() * 100
        
        # Risk metrics
        returns = self.trades_df['net_pnl'] / starting_capital
        volatility = returns.std() * np.sqrt(252) if len(returns) > 1 else 0
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        # Drawdown analysis
        max_drawdown = self.trades_df['drawdown_pct'].min()
        
        # Win/Loss analysis
        winning_trades = self.trades_df[self.trades_df['win_loss_flag'] == 1]
        losing_trades = self.trades_df[self.trades_df['win_loss_flag'] == 0]
        
        avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
        
        # Profit factor
        total_wins = winning_trades['net_pnl'].sum() if len(winning_trades) > 0 else 0
        total_losses = abs(losing_trades['net_pnl'].sum()) if len(losing_trades) > 0 else 1
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        # Strategy breakdown
        call_trades = self.trades_df[self.trades_df['strategy_type'] == 'CALL_SPREAD']
        put_trades = self.trades_df[self.trades_df['strategy_type'] == 'PUT_SPREAD']
        
        call_performance = {
            'trades': len(call_trades),
            'pnl': call_trades['net_pnl'].sum() if len(call_trades) > 0 else 0,
            'win_rate': call_trades['win_loss_flag'].mean() * 100 if len(call_trades) > 0 else 0,
            'avg_vix': call_trades['vix_level'].mean() if len(call_trades) > 0 else 0
        }
        
        put_performance = {
            'trades': len(put_trades),
            'pnl': put_trades['net_pnl'].sum() if len(put_trades) > 0 else 0,
            'win_rate': put_trades['win_loss_flag'].mean() * 100 if len(put_trades) > 0 else 0,
            'avg_vix': put_trades['vix_level'].mean() if len(put_trades) > 0 else 0
        }
        
        return {
            'total_trades': len(self.trades_df),
            'total_return': total_return,
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'volatility': volatility,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'starting_capital': starting_capital,
            'final_capital': starting_capital + total_pnl,
            'call_performance': call_performance,
            'put_performance': put_performance,
            'avg_vix': self.trades_df['vix_level'].mean(),
            'date_range': f"{self.trades_df['entry_date'].min().strftime('%Y-%m-%d')} to {self.trades_df['exit_date'].max().strftime('%Y-%m-%d')}"
        }
    
    def create_executive_summary_page(self, pdf, metrics, narrative):
        """Create executive summary page"""
        
        fig = plt.figure(figsize=(11, 8.5))
        fig.suptitle('VIX Regime Switching Strategy - Executive Summary', fontsize=16, fontweight='bold', y=0.95)
        
        # Create text areas
        gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], hspace=0.3, wspace=0.3)
        
        # Strategy Overview
        ax1 = fig.add_subplot(gs[0, :])
        ax1.axis('off')
        
        strategy_text = f"""
STRATEGY OVERVIEW

The VIX Regime Switching Strategy employs a sophisticated approach to options trading by adapting to market volatility conditions:

• LOW VIX (< 15): Execute CALL spreads to capture overnight S&P 500 bullish bias during calm market periods
• HIGH VIX (≥ 15): Execute PUT spreads to profit from market stress and protect against downside moves

Key Features:
• Adaptive regime switching based on VIX levels
• Conservative position sizing (5 contracts per trade)
• Overnight holding periods to capture specific market inefficiencies
• Debit spread structure limits maximum risk while providing defined profit potential
        """
        
        ax1.text(0.02, 0.95, strategy_text, transform=ax1.transAxes, fontsize=10,
                verticalalignment='top', fontweight='normal')
        
        # Performance Metrics
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.axis('off')
        
        performance_text = f"""
PERFORMANCE METRICS

Total Return: {metrics['total_return']:+.1f}%
Win Rate: {metrics['win_rate']:.1f}%
Total Trades: {metrics['total_trades']}
Max Drawdown: {metrics['max_drawdown']:.1f}%
Sharpe Ratio: {metrics['sharpe_ratio']:.2f}
Profit Factor: {metrics['profit_factor']:.2f}

Starting Capital: ${metrics['starting_capital']:,}
Final Capital: ${metrics['final_capital']:,}
Total P&L: ${metrics['total_pnl']:+,}

Average Win: ${metrics['avg_win']:+,.0f}
Average Loss: ${metrics['avg_loss']:+,.0f}
        """
        
        ax2.text(0.05, 0.95, performance_text, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')
        
        # Strategy Breakdown
        ax3 = fig.add_subplot(gs[1, 1])
        ax3.axis('off')
        
        breakdown_text = f"""
STRATEGY BREAKDOWN

CALL SPREADS (Low VIX):
Trades: {metrics['call_performance']['trades']}
P&L: ${metrics['call_performance']['pnl']:+,.0f}
Win Rate: {metrics['call_performance']['win_rate']:.1f}%
Avg VIX: {metrics['call_performance']['avg_vix']:.1f}

PUT SPREADS (High VIX):
Trades: {metrics['put_performance']['trades']}
P&L: ${metrics['put_performance']['pnl']:+,.0f}
Win Rate: {metrics['put_performance']['win_rate']:.1f}%
Avg VIX: {metrics['put_performance']['avg_vix']:.1f}

Trading Period: {metrics['date_range']}
Average VIX: {metrics['avg_vix']:.1f}
        """
        
        ax3.text(0.05, 0.95, breakdown_text, transform=ax3.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')
        
        # Market Narrative
        ax4 = fig.add_subplot(gs[2, :])
        ax4.axis('off')
        
        ax4.text(0.02, 0.95, narrative, transform=ax4.transAxes, fontsize=9,
                verticalalignment='top', fontweight='normal', wrap=True)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
    
    def create_equity_curve_page(self, pdf, metrics):
        """Create equity curve and performance charts page"""
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(11, 8.5))
        fig.suptitle('Performance Analysis & Equity Curve', fontsize=16, fontweight='bold')
        
        # 1. Equity Curve
        ax1.plot(self.trades_df['exit_date'], self.trades_df['portfolio_value'], 'b-', linewidth=2, label='Portfolio Value')
        ax1.axhline(y=metrics['starting_capital'], color='k', linestyle='--', alpha=0.5, label='Starting Capital')
        ax1.set_title('Portfolio Equity Curve')
        ax1.set_ylabel('Portfolio Value ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # Format y-axis
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # 2. Drawdown Chart
        ax2.fill_between(self.trades_df['exit_date'], 0, self.trades_df['drawdown_pct'], 
                         color='red', alpha=0.7, label='Drawdown %')
        ax2.set_title('Drawdown Analysis')
        ax2.set_ylabel('Drawdown (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Monthly P&L
        self.trades_df['month'] = self.trades_df['exit_date'].dt.to_period('M')
        monthly_pnl = self.trades_df.groupby('month')['net_pnl'].sum()
        colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
        
        ax3.bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors, alpha=0.7)
        ax3.set_title('Monthly P&L')
        ax3.set_ylabel('Monthly P&L ($)')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linewidth=1)
        
        # Format y-axis
        ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # 4. VIX vs Performance
        colors = ['blue' if x == 'CALL_SPREAD' else 'red' for x in self.trades_df['strategy_type']]
        scatter = ax4.scatter(self.trades_df['vix_level'], self.trades_df['net_pnl'], 
                             c=colors, alpha=0.7, s=50)
        ax4.axvline(x=15, color='black', linestyle='--', alpha=0.7, label='VIX Threshold (15)')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax4.set_title('VIX Level vs Trade P&L')
        ax4.set_xlabel('VIX Level')
        ax4.set_ylabel('Trade P&L ($)')
        ax4.legend(['VIX Threshold', 'Call Spreads', 'Put Spreads'])
        ax4.grid(True, alpha=0.3)
        
        # Format y-axis
        ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()

    def create_trade_table_page(self, pdf):
        """Create detailed trade table page"""

        fig = plt.figure(figsize=(11, 8.5))
        fig.suptitle('Detailed Trade History', fontsize=16, fontweight='bold', y=0.95)

        # Prepare trade data for table
        table_data = []
        for _, trade in self.trades_df.iterrows():
            strategy_short = "CALL" if trade['strategy_type'] == 'CALL_SPREAD' else "PUT"

            table_data.append([
                f"{trade['trade_number']:02d}",
                trade['entry_date'].strftime('%m/%d/%y'),
                trade['exit_date'].strftime('%m/%d/%y'),
                f"{trade['vix_level']:.1f}",
                strategy_short,
                f"{trade['spx_entry']:.0f}",
                f"{trade['spx_exit']:.0f}",
                f"{trade['spx_change']:+.0f}",
                f"{trade['long_strike']:.0f}",
                f"{trade['short_strike']:.0f}",
                f"{trade['contracts']}",
                f"${trade['net_debit']:.2f}",
                f"${trade['net_pnl']:+,.0f}",
                "W" if trade['win_loss_flag'] == 1 else "L"
            ])

        # Create table
        ax = fig.add_subplot(111)
        ax.axis('tight')
        ax.axis('off')

        # Table headers
        headers = ['#', 'Entry', 'Exit', 'VIX', 'Type', 'SPX In', 'SPX Out', 'SPX Δ',
                  'Long', 'Short', 'Qty', 'Debit', 'P&L', 'W/L']

        # Create table with alternating row colors
        table = ax.table(cellText=table_data[:20],  # Show first 20 trades
                        colLabels=headers,
                        cellLoc='center',
                        loc='center',
                        bbox=[0, 0.1, 1, 0.8])

        # Style the table
        table.auto_set_font_size(False)
        table.set_fontsize(8)
        table.scale(1, 1.5)

        # Color code headers
        for i in range(len(headers)):
            table[(0, i)].set_facecolor('#4472C4')
            table[(0, i)].set_text_props(weight='bold', color='white')

        # Color code rows
        for i in range(1, min(21, len(table_data) + 1)):
            row_color = '#F2F2F2' if i % 2 == 0 else 'white'
            for j in range(len(headers)):
                table[(i, j)].set_facecolor(row_color)

                # Color code W/L column
                if j == 13:  # W/L column
                    if table_data[i-1][j] == 'W':
                        table[(i, j)].set_facecolor('#90EE90')  # Light green
                    else:
                        table[(i, j)].set_facecolor('#FFB6C1')  # Light red

        # Add summary text
        if len(self.trades_df) > 20:
            summary_text = f"Showing first 20 of {len(self.trades_df)} total trades"
            fig.text(0.5, 0.05, summary_text, ha='center', fontsize=10, style='italic')

        pdf.savefig(fig, bbox_inches='tight')
        plt.close()

    def create_next_trade_page(self, pdf):
        """Create next trade recommendation page"""

        # Initialize strategy to get current VIX regime
        strategy = FinalVIXRegimeStrategy()

        # Load minimal data to get VIX reading
        strategy.spx_options_data = strategy.load_spx_options_data()
        if strategy.spx_options_data is not None:
            strategy.create_vix_proxy()
            regime_info, current_vix = strategy.get_current_vix_regime()
        else:
            regime_info = None
            current_vix = None

        fig = plt.figure(figsize=(11, 8.5))
        fig.suptitle('Next Trade Recommendation & Strategy Explanation', fontsize=16, fontweight='bold', y=0.95)

        # Create layout
        gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], hspace=0.3, wspace=0.3)

        # Current Market Regime
        ax1 = fig.add_subplot(gs[0, :])
        ax1.axis('off')

        if regime_info:
            regime_text = f"""
CURRENT MARKET REGIME & NEXT TRADE RECOMMENDATION

Current VIX Level: {regime_info['current_vix']:.1f}
Market Regime: {regime_info['regime']}
Recommended Strategy: {regime_info['next_strategy']}
Last Updated: {regime_info['date'].strftime('%Y-%m-%d')}

{regime_info['description']}
            """
        else:
            regime_text = """
CURRENT MARKET REGIME & NEXT TRADE RECOMMENDATION

Unable to determine current VIX level from available data.
Please check market conditions and VIX level before executing trades.

Strategy Rules:
• VIX < 15: Execute CALL spreads (overnight bullish bias)
• VIX ≥ 15: Execute PUT spreads (market stress protection)
            """

        ax1.text(0.02, 0.95, regime_text, transform=ax1.transAxes, fontsize=12,
                verticalalignment='top', fontweight='normal')

        # Trade Execution Details
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.axis('off')

        execution_text = """
TRADE EXECUTION DETAILS

Position Size: 5 contracts
Spread Width: 150 points
Strike Selection: 25 points OTM
Holding Period: Overnight (1 day)
Commission: $2 per contract per leg

CALL SPREAD (Low VIX):
• Buy call 25 points above SPX
• Sell call 150 points higher
• Profit if SPX rises overnight

PUT SPREAD (High VIX):
• Buy put 25 points below SPX
• Sell put 150 points lower
• Profit if SPX falls during stress
        """

        ax2.text(0.05, 0.95, execution_text, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')

        # Risk Management
        ax3 = fig.add_subplot(gs[1, 1])
        ax3.axis('off')

        risk_text = """
RISK MANAGEMENT

Maximum Risk: Net debit paid
Maximum Reward: Spread width - debit
Typical Risk: $500 - $2,500 per trade
Typical Reward: $1,000 - $7,500 per trade

FILTERS:
• Skip if debit < $5 or > $50
• Ensure options have 20+ days to expiry
• Only trade during regular market hours
• Monitor VIX for regime changes

POSITION SIZING:
Conservative 5-contract size limits
maximum loss while providing
meaningful profit potential.
        """

        ax3.text(0.05, 0.95, risk_text, transform=ax3.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')

        # Strategy Performance Summary
        ax4 = fig.add_subplot(gs[2, :])
        ax4.axis('off')

        metrics = self.calculate_performance_benchmarks()

        performance_text = f"""
STRATEGY PERFORMANCE SUMMARY

The VIX Regime Switching Strategy has demonstrated consistent performance across market conditions:

Historical Performance: {metrics['total_return']:+.1f}% total return with {metrics['win_rate']:.1f}% win rate
Risk Management: Maximum drawdown of {metrics['max_drawdown']:.1f}% with Sharpe ratio of {metrics['sharpe_ratio']:.2f}
Regime Effectiveness: Call spreads ({metrics['call_performance']['trades']} trades, {metrics['call_performance']['win_rate']:.1f}% win rate)
                     and Put spreads ({metrics['put_performance']['trades']} trades, {metrics['put_performance']['win_rate']:.1f}% win rate)

The strategy's adaptive nature allows it to profit in both calm and volatile market environments while maintaining
strict risk controls through conservative position sizing and defined-risk spread structures.
        """

        ax4.text(0.02, 0.95, performance_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', fontweight='normal')

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()

    def generate_comprehensive_report(self):
        """Generate the comprehensive professional report"""
        
        print("📊 GENERATING COMPREHENSIVE PROFESSIONAL REPORT")
        print("=" * 60)
        
        # Load strategy results
        if not self.load_strategy_results():
            return None
            
        # Calculate performance metrics
        metrics = self.calculate_performance_benchmarks()
        
        # Get market narrative
        narrative = self.get_chatgpt_market_narrative()
        
        # Create PDF report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"reports/VIX_Regime_Strategy_Report_{timestamp}.pdf"
        
        # Ensure reports directory exists
        os.makedirs('reports', exist_ok=True)
        
        with PdfPages(filename) as pdf:
            # Page 1: Executive Summary
            self.create_executive_summary_page(pdf, metrics, narrative)

            # Page 2: Equity Curve and Performance
            self.create_equity_curve_page(pdf, metrics)

            # Page 3: Trade Table
            self.create_trade_table_page(pdf)

            # Page 4: Next Trade Recommendation
            self.create_next_trade_page(pdf)

        print(f"✅ Comprehensive report generated: {filename}")
        return filename


if __name__ == "__main__":
    print("🎯 COMPREHENSIVE STRATEGY REPORT GENERATOR")
    print("=" * 50)
    
    report_generator = ComprehensiveStrategyReport()
    report_file = report_generator.generate_comprehensive_report()
    
    if report_file:
        print(f"\n✅ Professional report generated successfully!")
        print(f"📊 Report saved: {report_file}")
    else:
        print("❌ Report generation failed")
