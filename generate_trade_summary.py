#!/usr/bin/env python3
"""
Trade Summary Statistics Generator
Creates comprehensive summary of trade performance and validates data integrity
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime
import os

def generate_trade_summary():
    """Generate comprehensive trade summary statistics"""
    
    print("📊 GENERATING TRADE SUMMARY STATISTICS")
    print("=" * 50)
    
    # Load trades data
    try:
        trades_df = pd.read_csv('trades/call_spread_trades.csv')
        print(f"✅ Loaded {len(trades_df)} trades for analysis")
    except FileNotFoundError:
        print("❌ No trades file found. Run the strategy first.")
        return
    
    # Convert dates
    trades_df['signal_date'] = pd.to_datetime(trades_df['signal_date'])
    trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
    trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
    
    # Validate data integrity
    print("\n🔍 DATA VALIDATION")
    print("-" * 30)
    
    # Check for required columns
    required_columns = ['signal_date', 'entry_date', 'exit_date', 'signal_direction', 
                       'confidence_score', 'spx_price', 'net_pnl', 'win_loss_flag']
    missing_columns = [col for col in required_columns if col not in trades_df.columns]
    
    if missing_columns:
        print(f"⚠️ Missing columns: {missing_columns}")
    else:
        print("✅ All required columns present")
    
    # Check for new relative positioning columns
    relative_columns = ['spx_vs_ma20', 'spx_vs_ma50', 'ma_trend']
    has_relative_data = all(col in trades_df.columns for col in relative_columns)
    
    if has_relative_data:
        print("✅ Relative positioning data available")
    else:
        print("⚠️ Some relative positioning columns missing")
    
    # Data quality checks
    null_counts = trades_df.isnull().sum()
    if null_counts.sum() > 0:
        print(f"⚠️ Found {null_counts.sum()} null values")
        for col, count in null_counts[null_counts > 0].items():
            print(f"   {col}: {count} nulls")
    else:
        print("✅ No null values found")
    
    # Calculate comprehensive statistics
    starting_capital = 100000
    
    # Basic performance metrics
    total_pnl = trades_df['net_pnl'].sum()
    win_rate = trades_df['win_loss_flag'].mean() * 100
    total_return = (total_pnl / starting_capital) * 100
    
    # Calculate drawdown
    trades_df['cumulative_pnl'] = trades_df['net_pnl'].cumsum()
    trades_df['portfolio_value'] = starting_capital + trades_df['cumulative_pnl']
    trades_df['running_max'] = trades_df['portfolio_value'].expanding().max()
    trades_df['drawdown'] = trades_df['portfolio_value'] - trades_df['running_max']
    trades_df['drawdown_pct'] = (trades_df['drawdown'] / trades_df['running_max']) * 100
    max_drawdown = trades_df['drawdown_pct'].min()
    
    # Win/Loss analysis
    winning_trades = trades_df[trades_df['win_loss_flag'] == 1]
    losing_trades = trades_df[trades_df['win_loss_flag'] == 0]
    
    avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
    avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
    profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
    
    # Position sizing analysis
    avg_contracts = trades_df['contracts'].mean()
    min_contracts = trades_df['contracts'].min()
    max_contracts = trades_df['contracts'].max()
    
    # Credit analysis
    avg_credit = trades_df['net_credit'].mean()
    min_credit = trades_df['net_credit'].min()
    max_credit = trades_df['net_credit'].max()
    
    # Time analysis
    date_range = f"{trades_df['exit_date'].min().strftime('%Y-%m-%d')} to {trades_df['exit_date'].max().strftime('%Y-%m-%d')}"
    trading_days = (trades_df['exit_date'].max() - trades_df['exit_date'].min()).days
    
    # Create summary dictionary
    summary_stats = {
        'generation_timestamp': datetime.now().isoformat(),
        'data_validation': {
            'total_trades': int(len(trades_df)),
            'required_columns_present': len(missing_columns) == 0,
            'relative_positioning_data': has_relative_data,
            'null_values': int(null_counts.sum()),
            'date_range': date_range,
            'trading_period_days': int(trading_days)
        },
        'performance_metrics': {
            'total_return_pct': round(total_return, 2),
            'total_pnl': round(total_pnl, 2),
            'win_rate_pct': round(win_rate, 2),
            'max_drawdown_pct': round(max_drawdown, 2),
            'profit_factor': round(profit_factor, 2) if profit_factor != float('inf') else 'Infinite',
            'starting_capital': starting_capital,
            'final_capital': round(starting_capital + total_pnl, 2)
        },
        'trade_analysis': {
            'total_trades': int(len(trades_df)),
            'winning_trades': int(len(winning_trades)),
            'losing_trades': int(len(losing_trades)),
            'avg_win': float(round(avg_win, 2)),
            'avg_loss': float(round(avg_loss, 2)),
            'best_trade': float(round(trades_df['net_pnl'].max(), 2)),
            'worst_trade': float(round(trades_df['net_pnl'].min(), 2))
        },
        'position_sizing': {
            'avg_contracts': float(round(avg_contracts, 2)),
            'min_contracts': int(min_contracts),
            'max_contracts': int(max_contracts),
            'avg_credit': float(round(avg_credit, 2)),
            'min_credit': float(round(min_credit, 2)),
            'max_credit': float(round(max_credit, 2))
        },
        'strategy_features': {
            'signal_direction': trades_df['signal_direction'].iloc[0] if len(trades_df) > 0 else 'N/A',
            'avg_confidence_score': round(trades_df['confidence_score'].mean(), 3),
            'min_confidence': round(trades_df['confidence_score'].min(), 3),
            'max_confidence': round(trades_df['confidence_score'].max(), 3)
        }
    }
    
    # Add relative positioning analysis if available
    if has_relative_data:
        summary_stats['relative_positioning'] = {
            'avg_spx_vs_ma20': round(trades_df['spx_vs_ma20'].mean(), 2),
            'avg_spx_vs_ma50': round(trades_df['spx_vs_ma50'].mean(), 2),
            'avg_ma_trend': round(trades_df['ma_trend'].mean(), 2),
            'spx_vs_ma20_range': [round(trades_df['spx_vs_ma20'].min(), 2), round(trades_df['spx_vs_ma20'].max(), 2)],
            'ma_trend_range': [round(trades_df['ma_trend'].min(), 2), round(trades_df['ma_trend'].max(), 2)]
        }
    
    # Monthly performance breakdown
    trades_df['month'] = trades_df['exit_date'].dt.to_period('M')
    monthly_performance = trades_df.groupby('month').agg({
        'net_pnl': ['sum', 'count'],
        'win_loss_flag': 'mean'
    }).round(2)
    
    monthly_stats = {}
    for month in monthly_performance.index:
        monthly_stats[str(month)] = {
            'total_pnl': float(monthly_performance.loc[month, ('net_pnl', 'sum')]),
            'trade_count': int(monthly_performance.loc[month, ('net_pnl', 'count')]),
            'win_rate': float(monthly_performance.loc[month, ('win_loss_flag', 'mean')] * 100)
        }
    
    summary_stats['monthly_performance'] = monthly_stats
    
    # Save summary statistics
    os.makedirs('trades', exist_ok=True)
    
    # Save as JSON
    with open('trades/trade_summary_stats.json', 'w') as f:
        json.dump(summary_stats, f, indent=2)
    
    # Save as CSV for easy viewing
    summary_df = pd.DataFrame([
        ['Total Trades', len(trades_df)],
        ['Win Rate (%)', round(win_rate, 1)],
        ['Total Return (%)', round(total_return, 1)],
        ['Total P&L ($)', f"{total_pnl:,.0f}"],
        ['Max Drawdown (%)', round(max_drawdown, 1)],
        ['Average Win ($)', f"{avg_win:,.0f}"],
        ['Average Loss ($)', f"{avg_loss:,.0f}"],
        ['Profit Factor', round(profit_factor, 2) if profit_factor != float('inf') else 'Infinite'],
        ['Average Contracts', round(avg_contracts, 1)],
        ['Average Credit ($)', round(avg_credit, 2)],
        ['Date Range', date_range],
        ['Trading Period (Days)', trading_days]
    ], columns=['Metric', 'Value'])
    
    summary_df.to_csv('trades/performance_summary.csv', index=False)
    
    # Print summary to console
    print(f"\n📊 PERFORMANCE SUMMARY")
    print("=" * 40)
    print(f"📈 Total Return: {total_return:+.1f}%")
    print(f"🎯 Win Rate: {win_rate:.1f}%")
    print(f"💰 Total P&L: ${total_pnl:+,.0f}")
    print(f"📉 Max Drawdown: {max_drawdown:.1f}%")
    print(f"📊 Total Trades: {len(trades_df)}")
    print(f"🏆 Winning Trades: {len(winning_trades)}")
    print(f"💔 Losing Trades: {len(losing_trades)}")
    print(f"💪 Average Win: ${avg_win:,.0f}")
    print(f"📦 Average Contracts: {avg_contracts:.1f}")
    print(f"💵 Average Credit: ${avg_credit:.2f}")
    
    if has_relative_data:
        print(f"\n📊 RELATIVE POSITIONING")
        print("-" * 30)
        print(f"📈 Avg SPX vs 20-day MA: {trades_df['spx_vs_ma20'].mean():+.1f}%")
        print(f"📈 Avg SPX vs 50-day MA: {trades_df['spx_vs_ma50'].mean():+.1f}%")
        print(f"📊 Avg MA Trend: {trades_df['ma_trend'].mean():+.1f}%")
    
    print(f"\n✅ Summary statistics saved:")
    print(f"   📄 trades/trade_summary_stats.json")
    print(f"   📊 trades/performance_summary.csv")
    
    return summary_stats

def create_backup():
    """Create backup of successful trade data"""
    
    print("\n💾 CREATING TRADE DATA BACKUP")
    print("-" * 35)
    
    try:
        # Create backup with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"trades/call_spread_trades_backup_{timestamp}.csv"
        
        # Copy current trades file
        trades_df = pd.read_csv('trades/call_spread_trades.csv')
        trades_df.to_csv(backup_filename, index=False)
        
        print(f"✅ Backup created: {backup_filename}")
        print(f"📊 Backed up {len(trades_df)} trades")
        
        return backup_filename
        
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return None

if __name__ == "__main__":
    summary_stats = generate_trade_summary()
    backup_file = create_backup()
    
    if summary_stats and backup_file:
        print(f"\n🎯 TRADE ANALYSIS COMPLETE")
        print("=" * 40)
        print("✅ All trade data validated and summarized")
        print("✅ Backup created successfully")
        print("✅ Ready for comprehensive reporting")
