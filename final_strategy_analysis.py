#!/usr/bin/env python3
"""
Final Comprehensive Strategy Analysis
Compare all overnight strategies including the enhanced VIX regime switching
"""

import pandas as pd
import numpy as np

def load_all_strategies():
    """Load all strategy results"""
    
    strategies = {}
    
    # Simple overnight strategy
    try:
        simple_df = pd.read_csv('trades/simple_overnight_trades.csv')
        simple_df['date'] = pd.to_datetime(simple_df['date'])
        strategies['Simple Overnight'] = simple_df
        print(f"✅ Simple Overnight: {len(simple_df)} trades")
    except FileNotFoundError:
        print("❌ Simple overnight results not found")
    
    # Original VIX regime switching
    try:
        vix_df = pd.read_csv('trades/vix_regime_switching_trades.csv')
        vix_df['date'] = pd.to_datetime(vix_df['date'])
        strategies['VIX Regime (Original)'] = vix_df
        print(f"✅ VIX Regime (Original): {len(vix_df)} trades")
    except FileNotFoundError:
        print("❌ VIX regime switching results not found")
    
    # Enhanced VIX regime switching
    try:
        enhanced_df = pd.read_csv('trades/enhanced_vix_regime_trades.csv')
        enhanced_df['date'] = pd.to_datetime(enhanced_df['date'])
        strategies['Enhanced VIX Regime'] = enhanced_df
        print(f"✅ Enhanced VIX Regime: {len(enhanced_df)} trades")
    except FileNotFoundError:
        print("❌ Enhanced VIX regime results not found")
    
    # Hybrid VRP strategy
    try:
        hybrid_df = pd.read_csv('trades/hybrid_vrp_trades.csv')
        hybrid_df['date'] = pd.to_datetime(hybrid_df['date'])
        strategies['Hybrid VRP'] = hybrid_df
        print(f"✅ Hybrid VRP: {len(hybrid_df)} trades")
    except FileNotFoundError:
        print("⚠️ Hybrid VRP results not found (optional)")
    
    return strategies

def calculate_comprehensive_metrics(df, strategy_name):
    """Calculate comprehensive metrics"""
    
    if len(df) == 0:
        return None
    
    # Basic performance
    total_pnl = df['net_pnl'].sum()
    win_rate = df['win_loss_flag'].mean() * 100
    total_return = (total_pnl / 100000) * 100
    
    # Win/Loss analysis
    winning_trades = df[df['win_loss_flag'] == 1]
    losing_trades = df[df['win_loss_flag'] == 0]
    
    avg_win = winning_trades['net_pnl'].mean() if len(winning_trades) > 0 else 0
    avg_loss = losing_trades['net_pnl'].mean() if len(losing_trades) > 0 else 0
    
    # Profit factor
    total_wins = winning_trades['net_pnl'].sum() if len(winning_trades) > 0 else 0
    total_losses = abs(losing_trades['net_pnl'].sum()) if len(losing_trades) > 0 else 1
    profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
    
    # Risk metrics
    returns = df['net_pnl'] / 100000
    volatility = returns.std() * np.sqrt(252) if len(returns) > 1 else 0
    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
    
    # Drawdown
    df_sorted = df.sort_values('date')
    df_sorted['cumulative_pnl'] = df_sorted['net_pnl'].cumsum()
    df_sorted['portfolio_value'] = 100000 + df_sorted['cumulative_pnl']
    df_sorted['running_max'] = df_sorted['portfolio_value'].expanding().max()
    df_sorted['drawdown_pct'] = ((df_sorted['portfolio_value'] - df_sorted['running_max']) / df_sorted['running_max']) * 100
    max_drawdown = df_sorted['drawdown_pct'].min()
    
    # Market analysis
    spx_up_pct = (df['spx_change'] > 0).mean() * 100 if 'spx_change' in df.columns else 0
    avg_spx_change = df['spx_change'].mean() if 'spx_change' in df.columns else 0
    
    # VIX analysis (if available)
    avg_vix = df['vix'].mean() if 'vix' in df.columns else 0
    
    return {
        'strategy_name': strategy_name,
        'total_trades': len(df),
        'total_pnl': total_pnl,
        'total_return': total_return,
        'win_rate': win_rate,
        'winning_trades': len(winning_trades),
        'losing_trades': len(losing_trades),
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'profit_factor': profit_factor,
        'max_drawdown': max_drawdown,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'spx_up_pct': spx_up_pct,
        'avg_spx_change': avg_spx_change,
        'avg_vix': avg_vix
    }

def analyze_enhanced_strategy_details(enhanced_df):
    """Analyze the enhanced strategy in detail"""
    
    print("\n📊 ENHANCED VIX STRATEGY DETAILED ANALYSIS")
    print("=" * 60)
    
    if 'spread_type' not in enhanced_df.columns:
        print("❌ No spread type data available")
        return
    
    # Analyze by spread type
    debit_trades = enhanced_df[enhanced_df['spread_type'] == 'DEBIT']
    credit_trades = enhanced_df[enhanced_df['spread_type'] == 'CREDIT']
    
    print(f"📈 DEBIT SPREADS (Low VIX < 15):")
    if len(debit_trades) > 0:
        debit_pnl = debit_trades['net_pnl'].sum()
        debit_win_rate = debit_trades['win_loss_flag'].mean() * 100
        debit_avg_vix = debit_trades['vix'].mean()
        print(f"   Trades: {len(debit_trades)}")
        print(f"   P&L: ${debit_pnl:+,.0f}")
        print(f"   Win Rate: {debit_win_rate:.1f}%")
        print(f"   Average VIX: {debit_avg_vix:.1f}")
    else:
        print("   No debit spread trades")
    
    print(f"\n📉 CREDIT SPREADS (High VIX ≥ 15):")
    if len(credit_trades) > 0:
        credit_pnl = credit_trades['net_pnl'].sum()
        credit_win_rate = credit_trades['win_loss_flag'].mean() * 100
        credit_avg_vix = credit_trades['vix'].mean()
        print(f"   Trades: {len(credit_trades)}")
        print(f"   P&L: ${credit_pnl:+,.0f}")
        print(f"   Win Rate: {credit_win_rate:.1f}%")
        print(f"   Average VIX: {credit_avg_vix:.1f}")
        
        # Analyze credit spread performance by VIX level
        print(f"\n   📊 CREDIT SPREAD PERFORMANCE BY VIX LEVEL:")
        vix_ranges = pd.cut(credit_trades['vix'], bins=[0, 20, 25, 30, 100], labels=['15-20', '20-25', '25-30', '30+'])
        vix_analysis = credit_trades.groupby(vix_ranges).agg({
            'net_pnl': ['count', 'sum', 'mean'],
            'win_loss_flag': 'mean'
        }).round(2)
        
        for vix_range in vix_analysis.index:
            if pd.isna(vix_range):
                continue
            trade_count = int(vix_analysis.loc[vix_range, ('net_pnl', 'count')])
            total_pnl = vix_analysis.loc[vix_range, ('net_pnl', 'sum')]
            win_rate = vix_analysis.loc[vix_range, ('win_loss_flag', 'mean')] * 100
            print(f"      VIX {vix_range}: {trade_count} trades, ${total_pnl:+,.0f} P&L, {win_rate:.1f}% win rate")
    else:
        print("   No credit spread trades")

def create_final_comparison_table(strategies):
    """Create final comparison table"""
    
    print("\n📊 FINAL STRATEGY COMPARISON")
    print("=" * 120)
    
    metrics_list = []
    for name, df in strategies.items():
        metrics = calculate_comprehensive_metrics(df, name)
        if metrics:
            metrics_list.append(metrics)
    
    if not metrics_list:
        print("❌ No strategy metrics to compare")
        return
    
    # Header
    print(f"{'Strategy':<25} {'Trades':<8} {'Return':<8} {'Win Rate':<10} {'Avg Win':<10} {'Avg Loss':<10} {'Max DD':<8} {'Sharpe':<8} {'Avg VIX':<8}")
    print("-" * 120)
    
    # Data rows
    for metrics in metrics_list:
        vix_display = f"{metrics['avg_vix']:.1f}" if metrics['avg_vix'] > 0 else "N/A"
        print(f"{metrics['strategy_name']:<25} "
              f"{metrics['total_trades']:<8} "
              f"{metrics['total_return']:+6.1f}% "
              f"{metrics['win_rate']:8.1f}% "
              f"${metrics['avg_win']:8,.0f} "
              f"${metrics['avg_loss']:8,.0f} "
              f"{metrics['max_drawdown']:6.1f}% "
              f"{metrics['sharpe_ratio']:6.2f} "
              f"{vix_display:<8}")
    
    # Find best performers
    best_return = max(metrics_list, key=lambda x: x['total_return'])
    best_win_rate = max(metrics_list, key=lambda x: x['win_rate'])
    best_sharpe = max(metrics_list, key=lambda x: x['sharpe_ratio'])
    
    print(f"\n🏆 BEST PERFORMERS:")
    print(f"   📈 Highest Return: {best_return['strategy_name']} ({best_return['total_return']:+.1f}%)")
    print(f"   🎯 Highest Win Rate: {best_win_rate['strategy_name']} ({best_win_rate['win_rate']:.1f}%)")
    print(f"   ⚖️ Best Risk-Adjusted: {best_sharpe['strategy_name']} (Sharpe: {best_sharpe['sharpe_ratio']:.2f})")

def provide_final_recommendations():
    """Provide final strategy recommendations"""
    
    print(f"\n🚀 FINAL STRATEGY RECOMMENDATIONS")
    print("=" * 50)
    
    print(f"📊 KEY FINDINGS:")
    print(f"   1. Simple overnight strategy works best during low volatility periods")
    print(f"   2. Credit call spreads during high VIX periods are challenging")
    print(f"   3. VIX regime switching adds sophistication but may reduce returns")
    print(f"   4. Overnight S&P bullish bias is strongest when VIX < 15")
    
    print(f"\n💡 OPTIMAL STRATEGY SELECTION:")
    print(f"   🥇 PRIMARY: Simple Overnight Strategy")
    print(f"      • Use during confirmed low volatility periods (VIX < 15)")
    print(f"      • Debit call spreads: Buy 25 pts OTM, sell 150 pts further out")
    print(f"      • 5 contracts, overnight hold")
    
    print(f"\n   🥈 SECONDARY: VIX-Filtered Simple Strategy")
    print(f"      • Only trade when VIX < 15")
    print(f"      • Skip trading during high VIX periods (≥15)")
    print(f"      • Same mechanics as simple strategy")
    
    print(f"\n   🥉 ADVANCED: Enhanced Regime Switching (with caution)")
    print(f"      • Low VIX: Debit call spreads")
    print(f"      • High VIX: Consider avoiding or use different strategies")
    print(f"      • Credit call spreads during high VIX showed poor performance")
    
    print(f"\n⚠️ AVOID:")
    print(f"   • Credit call spreads during high volatility periods")
    print(f"   • Trading during VIX > 25 (high risk)")
    print(f"   • Large position sizes (stick to 5 contracts)")
    
    print(f"\n🎯 IMPLEMENTATION GUIDELINES:")
    print(f"   • Monitor VIX levels daily")
    print(f"   • Trade only when VIX < 15 for optimal results")
    print(f"   • Use 25-point OTM strikes with 150-point spread width")
    print(f"   • Maintain overnight holding period discipline")
    print(f"   • Conservative position sizing (5 contracts)")

def main():
    """Main analysis function"""
    
    print("🎯 FINAL COMPREHENSIVE OVERNIGHT STRATEGY ANALYSIS")
    print("=" * 70)
    
    # Load all strategies
    strategies = load_all_strategies()
    
    if not strategies:
        print("❌ No strategy results found")
        return
    
    # Create final comparison
    create_final_comparison_table(strategies)
    
    # Analyze enhanced strategy details
    if 'Enhanced VIX Regime' in strategies:
        analyze_enhanced_strategy_details(strategies['Enhanced VIX Regime'])
    
    # Provide final recommendations
    provide_final_recommendations()
    
    print(f"\n✅ FINAL ANALYSIS COMPLETE")
    print("=" * 40)

if __name__ == "__main__":
    main()
